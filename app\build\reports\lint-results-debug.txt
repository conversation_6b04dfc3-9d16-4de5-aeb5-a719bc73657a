D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\data\remote\model\HuggingFaceModels.kt:130: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "Company: $companyName (${String.format("%.2f", companyConfidence)}), " +
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\data\remote\model\HuggingFaceModels.kt:131: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "Type: $emailType (${String.format("%.2f", emailTypeConfidence)}), " +
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\data\remote\model\HuggingFaceModels.kt:132: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "Overall: ${String.format("%.2f", overallConfidence)}, " +
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt:157: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    "Ortalama Güven" to String.format("%.2f", stats.avgConfidence)
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt:234: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "Güven: ${String.format("%.2f", result.stage1_companyConfidence)}",
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt:283: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Güven: ${String.format("%.2f", result.stage1_companyConfidence)}",
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt:336: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "Güven: ${String.format("%.2f", confidence)}",
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:19: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application or sdk runs on a version of Android that is more
   recent than your targetSdkVersion specifies that it has been tested with,
   various compatibility modes kick in. This ensures that your application
   continues to work, but it may look out of place. For example, if the
   targetSdkVersion is less than 14, your app may get an option button in the
   UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:30: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2) [AndroidGradlePluginVersion]
agp = "8.8.0"
      ~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2) [AndroidGradlePluginVersion]
agp = "8.8.0"
      ~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2) [AndroidGradlePluginVersion]
agp = "8.8.0"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:68: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core-ktx:1.12.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:69: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:70: Warning: A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1 [GradleDependency]
    implementation 'androidx.activity:activity-compose:1.8.2' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:82: Warning: A newer version of com.google.android.gms:play-services-auth than 21.0.0 is available: 21.3.0 [GradleDependency]
    implementation 'com.google.android.gms:play-services-auth:21.0.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:90: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:101: Warning: A newer version of com.google.auth:google-auth-library-oauth2-http than 1.23.0 is available: 1.30.0 [GradleDependency]
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.23.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:102: Warning: A newer version of com.google.auth:google-auth-library-credentials than 1.23.0 is available: 1.30.0 [GradleDependency]
    implementation 'com.google.auth:google-auth-library-credentials:1.23.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:105: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1 [GradleDependency]
    implementation "androidx.room:room-runtime:2.6.1" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:106: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1 [GradleDependency]
    kapt "androidx.room:room-compiler:2.6.1"
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:107: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1 [GradleDependency]
    implementation "androidx.room:room-ktx:2.6.1"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:110: Warning: A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1 [GradleDependency]
    implementation "androidx.work:work-runtime-ktx:2.9.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:115: Warning: A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0 [GradleDependency]
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0' // Stabil (veya 1.2.0-beta01)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:116: Warning: A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0 [GradleDependency]
    implementation "androidx.hilt:hilt-work:1.1.0" // Stabil (veya 1.2.0-beta01)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:117: Warning: A newer version of androidx.hilt:hilt-compiler than 1.1.0 is available: 1.2.0 [GradleDependency]
    kapt "androidx.hilt:hilt-compiler:1.1.0"      // (androidx olan, hilt-work ve hilt-navigation-compose için)
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:120: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:121: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:122: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.0 [GradleDependency]
    implementation "androidx.lifecycle:lifecycle-runtime-compose:2.7.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:126: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:127: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0 [GradleDependency]
coreKtx = "1.15.0"
          ~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0 [GradleDependency]
coreKtx = "1.15.0"
          ~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0 [GradleDependency]
coreKtx = "1.15.0"
          ~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.8.7"
                      ~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.8.7"
                      ~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.8.7"
                      ~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.04.01"
             ~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.04.01"
             ~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.04.01"
             ~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client\1.43.3\a758b82e55a2f5f681e289c5ed384d3dbda6f3cd\google-http-client-1.43.3.jar: Warning: checkClientTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]
C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client\1.43.3\a758b82e55a2f5f681e289c5ed384d3dbda6f3cd\google-http-client-1.43.3.jar: Warning: checkServerTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]

   Explanation for issues of type "TrustAllX509TrustManager":
   This check looks for X509TrustManager implementations whose
   checkServerTrusted or checkClientTrusted methods do nothing (thus trusting
   any certificate chain) which could result in insecure network traffic
   caused by trusting arbitrary TLS/SSL certificates presented by peers.

   https://goo.gle/TrustAllX509TrustManager

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\xml\network_security_config.xml:17: Warning: The Network Security Configuration allows the use of user certificates in the release version of your app [AcceptsUserCertificates]
            <certificates src="user"/>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AcceptsUserCertificates":
   Allowing user certificates could allow eavesdroppers to intercept data sent
   by your app, which could impact the privacy of your users. Consider nesting
   your app's trust-anchors inside a <debug-overrides> element to make sure
   they are only available when android:debuggable is set to true.

   https://goo.gle/AcceptsUserCertificates
   https://developer.android.com/training/articles/security-config#TrustingDebugCa

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\xml\network_security_config.xml:14: Warning: Insecure Base Configuration [InsecureBaseConfiguration]
    <base-config cleartextTrafficPermitted="true">
                                            ~~~~

   Explanation for issues of type "InsecureBaseConfiguration":
   Permitting cleartext traffic could allow eavesdroppers to intercept data
   sent by your app, which impacts the privacy of your users. Consider only
   allowing encrypted traffic by setting the cleartextTrafficPermitted tag to
   false.

   https://goo.gle/InsecureBaseConfiguration
   https://developer.android.com/preview/features/security-config.html

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:106: Warning: This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp [KaptUsageInsteadOfKsp]
    kapt "androidx.room:room-compiler:2.6.1"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "KaptUsageInsteadOfKsp":
   KSP is a more efficient replacement for kapt. For libraries that support
   both, KSP should be used to improve build times.

   https://developer.android.com/studio/build/migrate-to-ksp

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt:41: Information: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var selectedTab by remember { mutableStateOf(0) }
                                  ~~~~~~~~~~~~~~

   Explanation for issues of type "AutoboxingStateCreation":
   Calling mutableStateOf<T>() when T is either backed by a primitive type on
   the JVM or is a value class results in a state implementation that requires
   all state values to be boxed. This usually causes an additional allocation
   for each state write, and adds some additional work to auto-unbox values
   when reading the value of the state. Instead, prefer to use a specialized
   primitive state implementation for Int, Long, Float, and Double when the
   state does not need to track null values and does not override the default
   SnapshotMutationPolicy. See mutableIntStateOf(), mutableLongStateOf(),
   mutableFloatStateOf(), and mutableDoubleStateOf() for more information.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.runtime
   Feedback: https://issuetracker.google.com/issues/new?component=612128

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.error_loading_subscriptions appears to be unused [UnusedResources]
    <string name="error_loading_subscriptions">Abonelikler yüklenirken hata oluştu.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.feedback_submitted appears to be unused [UnusedResources]
    <string name="feedback_submitted">Geri bildiriminiz gönderildi.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:68: Warning: Use version catalog instead (androidx.core:core-ktx is already available as androidx-core-ktx, but using version 1.15.0 instead) [UseTomlInstead]
    implementation 'androidx.core:core-ktx:1.12.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:69: Warning: Use version catalog instead (androidx.lifecycle:lifecycle-runtime-ktx is already available as androidx-lifecycle-runtime-ktx, but using version 2.8.7 instead) [UseTomlInstead]
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:70: Warning: Use version catalog instead (androidx.activity:activity-compose is already available as androidx-activity-compose, but using version 1.10.1 instead) [UseTomlInstead]
    implementation 'androidx.activity:activity-compose:1.8.2' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:71: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0") // Veya OkHttp3'ün diğer modülleriyle uyumlu versiyon
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:74: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.compose.ui:ui'
                   ~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:75: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.compose.ui:ui-graphics'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:76: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.compose.ui:ui-tooling-preview'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:77: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.compose.material3:material3'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:78: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.compose.material:material-icons-core' // BOM yönetir
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:79: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.compose.material:material-icons-extended' // BOM yönetir
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:82: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.android.gms:play-services-auth:21.0.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:85: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.retrofit2:retrofit:2.9.0' // Stabil, gerekirse güncelleyin
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:86: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:87: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:90: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:101: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.23.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:102: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.auth:google-auth-library-credentials:1.23.0' // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:105: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.room:room-runtime:2.6.1" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:106: Warning: Use version catalog instead [UseTomlInstead]
    kapt "androidx.room:room-compiler:2.6.1"
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:107: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.room:room-ktx:2.6.1"
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:110: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.work:work-runtime-ktx:2.9.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:113: Warning: Use version catalog instead [UseTomlInstead]
    implementation "com.google.dagger:hilt-android:2.49" // Proje seviyesindeki plugin ile aynı versiyonu kullanın (örn: 2.49)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:114: Warning: Use version catalog instead [UseTomlInstead]
    kapt "com.google.dagger:hilt-compiler:2.49"     // Proje seviyesindeki plugin ile aynı versiyonu kullanın (örn: 2.49)
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:115: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0' // Stabil (veya 1.2.0-beta01)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:116: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.hilt:hilt-work:1.1.0" // Stabil (veya 1.2.0-beta01)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:117: Warning: Use version catalog instead [UseTomlInstead]
    kapt "androidx.hilt:hilt-compiler:1.1.0"      // (androidx olan, hilt-work ve hilt-navigation-compose için)
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:120: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:121: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:122: Warning: Use version catalog instead [UseTomlInstead]
    implementation "androidx.lifecycle:lifecycle-runtime-compose:2.7.0" // Stabil
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:125: Warning: Use the existing version catalog reference (libs.junit) instead [UseTomlInstead]
    testImplementation 'junit:junit:4.13.2'
                       ~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:126: Warning: Use version catalog instead (androidx.test.ext:junit is already available as androidx-junit, but using version 1.2.1 instead) [UseTomlInstead]
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:127: Warning: Use version catalog instead (androidx.test.espresso:espresso-core is already available as androidx-espresso-core, but using version 3.6.1 instead) [UseTomlInstead]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:128: Warning: Use version catalog instead [UseTomlInstead]
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4' // BOM yönetir
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:129: Warning: Use version catalog instead [UseTomlInstead]
    debugImplementation 'androidx.compose.ui:ui-tooling' // BOM yönetir
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle:130: Warning: Use version catalog instead [UseTomlInstead]
    debugImplementation 'androidx.compose.ui:ui-test-manifest' // BOM yönetir
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

0 errors, 89 warnings
