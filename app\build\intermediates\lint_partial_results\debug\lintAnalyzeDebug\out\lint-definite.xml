<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.kt"
            line="130"
            column="42"
            startOffset="4486"
            endLine="130"
            endColumn="82"
            endOffset="4526"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.kt"
            line="131"
            column="38"
            startOffset="4571"
            endLine="131"
            endColumn="80"
            endOffset="4613"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.kt"
            line="132"
            column="29"
            startOffset="4649"
            endLine="132"
            endColumn="69"
            endOffset="4689"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.kt"
            line="157"
            column="41"
            startOffset="5167"
            endLine="157"
            endColumn="83"
            endOffset="5209"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.kt"
            line="234"
            column="42"
            startOffset="7876"
            endLine="234"
            endColumn="96"
            endOffset="7930"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.kt"
            line="283"
            column="38"
            startOffset="9615"
            endLine="283"
            endColumn="92"
            endOffset="9669"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.kt"
            line="336"
            column="42"
            startOffset="11413"
            endLine="336"
            endColumn="75"
            endOffset="11446"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="19"
            column="9"
            startOffset="574"
            endLine="19"
            endColumn="21"
            endOffset="586"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="30"
            column="13"
            startOffset="1453"
            endLine="30"
            endColumn="45"
            endOffset="1485"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.0"
                family="Update versions"
                oldString="8.8.0"
                replacement="8.10.0"
                priority="0"/>
            <fix-replace
                description="Change to 8.8.2"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.8.0"
                replacement="8.8.2"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="68"
            column="20"
            startOffset="2287"
            endLine="68"
            endColumn="51"
            endOffset="2318"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="69"
            column="20"
            startOffset="2348"
            endLine="69"
            endColumn="68"
            endOffset="2396"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="70"
            column="20"
            startOffset="2426"
            endLine="70"
            endColumn="62"
            endOffset="2468"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.gms:play-services-auth than 21.0.0 is available: 21.3.0">
        <fix-replace
            description="Change to 21.3.0"
            family="Update versions"
            oldString="21.0.0"
            replacement="21.3.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="82"
            column="20"
            startOffset="3098"
            endLine="82"
            endColumn="70"
            endOffset="3148"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="90"
            column="20"
            startOffset="3474"
            endLine="90"
            endColumn="54"
            endOffset="3508"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.auth:google-auth-library-oauth2-http than 1.23.0 is available: 1.30.0">
        <fix-replace
            description="Change to 1.30.0"
            family="Update versions"
            oldString="1.23.0"
            replacement="1.30.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="101"
            column="20"
            startOffset="4071"
            endLine="101"
            endColumn="76"
            endOffset="4127"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.auth:google-auth-library-credentials than 1.23.0 is available: 1.30.0">
        <fix-replace
            description="Change to 1.30.0"
            family="Update versions"
            oldString="1.23.0"
            replacement="1.30.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="102"
            column="20"
            startOffset="4157"
            endLine="102"
            endColumn="76"
            endOffset="4213"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="105"
            column="20"
            startOffset="4256"
            endLine="105"
            endColumn="54"
            endOffset="4290"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="106"
            column="10"
            startOffset="4310"
            endLine="106"
            endColumn="45"
            endOffset="4345"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="107"
            column="20"
            startOffset="4365"
            endLine="107"
            endColumn="50"
            endOffset="4395"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1">
        <fix-replace
            description="Change to 2.10.1"
            family="Update versions"
            oldString="2.9.0"
            replacement="2.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="110"
            column="20"
            startOffset="4435"
            endLine="110"
            endColumn="58"
            endOffset="4473"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="115"
            column="20"
            startOffset="4814"
            endLine="115"
            endColumn="65"
            endOffset="4859"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="116"
            column="20"
            startOffset="4909"
            endLine="116"
            endColumn="51"
            endOffset="4940"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-compiler than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="117"
            column="10"
            startOffset="4980"
            endLine="117"
            endColumn="45"
            endOffset="5015"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="120"
            column="20"
            startOffset="5120"
            endLine="120"
            endColumn="70"
            endOffset="5170"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="121"
            column="20"
            startOffset="5200"
            endLine="121"
            endColumn="74"
            endOffset="5254"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="122"
            column="20"
            startOffset="5284"
            endLine="122"
            endColumn="72"
            endOffset="5336"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="126"
            column="31"
            startOffset="5449"
            endLine="126"
            endColumn="62"
            endOffset="5480"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="127"
            column="31"
            startOffset="5511"
            endLine="127"
            endColumn="75"
            endOffset="5555"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.15.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="52"
            endLine="4"
            endColumn="19"
            endOffset="60"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.8.7"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="23"
            startOffset="146"
            endLine="8"
            endColumn="30"
            endOffset="153"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01">
        <fix-replace
            description="Change to 2025.05.01"
            family="Update versions"
            oldString="2024.04.01"
            replacement="2025.05.01"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="14"
            startOffset="194"
            endLine="10"
            endColumn="26"
            endOffset="206"/>
    </incident>

    <incident
        id="TrustAllX509TrustManager"
        severity="warning"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="$GRADLE_USER_HOME/caches/modules-2/files-2.1/com.google.http-client/google-http-client/1.43.3/a758b82e55a2f5f681e289c5ed384d3dbda6f3cd/google-http-client-1.43.3.jar"/>
    </incident>

    <incident
        id="TrustAllX509TrustManager"
        severity="warning"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="$GRADLE_USER_HOME/caches/modules-2/files-2.1/com.google.http-client/google-http-client/1.43.3/a758b82e55a2f5f681e289c5ed384d3dbda6f3cd/google-http-client-1.43.3.jar"/>
    </incident>

    <incident
        id="AcceptsUserCertificates"
        severity="warning"
        message="The Network Security Configuration allows the use of user certificates in the release version of your app">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="17"
            column="13"
            startOffset="630"
            endLine="17"
            endColumn="39"
            endOffset="656"/>
    </incident>

    <incident
        id="InsecureBaseConfiguration"
        severity="warning"
        message="Insecure Base Configuration">
        <fix-replace
            description="Replace with false"
            replacement="false"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
                startOffset="546"
                endOffset="550"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="14"
            column="45"
            startOffset="546"
            endLine="14"
            endColumn="49"
            endOffset="550"/>
    </incident>

    <incident
        id="KaptUsageInsteadOfKsp"
        severity="warning"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp">
        <show-url
            description="Learn about how to enable KSP and use the KSP processor for this dependency instead"
            url="https://developer.android.com/studio/build/migrate-to-ksp"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="106"
            column="5"
            startOffset="4305"
            endLine="106"
            endColumn="45"
            endOffset="4345"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.kt"
                startOffset="1660"
                endOffset="1674"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.kt"
            line="41"
            column="35"
            startOffset="1660"
            endLine="41"
            endColumn="49"
            endOffset="1674"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (androidx.core:core-ktx is already available as `androidx-core-ktx`, but using version 1.15.0 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for core-ktx-v1120"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with androidxCoreKtx = &quot;1.12.0&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="androidxCoreKtx = &quot;1.12.0&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="25"
                        endOffset="25"/>
                </fix-replace>
                <fix-replace
                    description="Replace with core-ktx-v1120 = { module = &quot;androidx.core:core-ktx&quot;, version.ref = &quot;androidxCoreKtx&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="core-ktx-v1120 = { module = &quot;androidx.core:core-ktx&quot;, version.ref = &quot;androidxCoreKtx&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="312"
                        endOffset="312"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.core.ktx.v1120"
                    robot="true"
                    independent="true"
                    replacement="libs.core.ktx.v1120"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="2287"
                        endOffset="2318"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `androidx-core-ktx` (version 1.15.0)"
                replacement="libs.androidx.core.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2287"
                    endOffset="2318"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="68"
            column="20"
            startOffset="2287"
            endLine="68"
            endColumn="51"
            endOffset="2318"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (androidx.lifecycle:lifecycle-runtime-ktx is already available as `androidx-lifecycle-runtime-ktx`, but using version 2.8.7 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for lifecycle-runtime-ktx-v270"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with androidxLifecycleRuntimeKtx = &quot;2.7.0&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="androidxLifecycleRuntimeKtx = &quot;2.7.0&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="25"
                        endOffset="25"/>
                </fix-replace>
                <fix-replace
                    description="Replace with lifecycle-runtime-ktx-v270 = { module = &quot;androidx.lifecycle:lifecycle-runtime-ktx&quot;, version.ref = &quot;androidxLifecycleRuntimeKtx&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="lifecycle-runtime-ktx-v270 = { module = &quot;androidx.lifecycle:lifecycle-runtime-ktx&quot;, version.ref = &quot;androidxLifecycleRuntimeKtx&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="1515"
                        endOffset="1515"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.lifecycle.runtime.ktx.v270"
                    robot="true"
                    independent="true"
                    replacement="libs.lifecycle.runtime.ktx.v270"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="2348"
                        endOffset="2396"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `androidx-lifecycle-runtime-ktx` (version 2.8.7)"
                replacement="libs.androidx.lifecycle.runtime.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2348"
                    endOffset="2396"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="69"
            column="20"
            startOffset="2348"
            endLine="69"
            endColumn="68"
            endOffset="2396"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (androidx.activity:activity-compose is already available as `androidx-activity-compose`, but using version 1.10.1 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for activity-compose-v182"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with androidxActivityCompose = &quot;1.8.2&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="androidxActivityCompose = &quot;1.8.2&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="25"
                        endOffset="25"/>
                </fix-replace>
                <fix-replace
                    description="Replace with activity-compose-v182 = { module = &quot;androidx.activity:activity-compose&quot;, version.ref = &quot;androidxActivityCompose&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="activity-compose-v182 = { module = &quot;androidx.activity:activity-compose&quot;, version.ref = &quot;androidxActivityCompose&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="220"
                        endOffset="220"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.activity.compose.v182"
                    robot="true"
                    independent="true"
                    replacement="libs.activity.compose.v182"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="2426"
                        endOffset="2468"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `androidx-activity-compose` (version 1.10.1)"
                replacement="libs.androidx.activity.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2426"
                    endOffset="2468"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="70"
            column="20"
            startOffset="2426"
            endLine="70"
            endColumn="62"
            endOffset="2468"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for squareup-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with squareupLoggingInterceptor = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupLoggingInterceptor = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with squareup-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;squareupLoggingInterceptor&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareup-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;squareupLoggingInterceptor&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.squareup.logging.interceptor"
                robot="true"
                replacement="libs.squareup.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2498"
                    endOffset="2547"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="71"
            column="20"
            startOffset="2498"
            endLine="71"
            endColumn="69"
            endOffset="2547"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-ui-ui"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-ui-ui = { module = &quot;androidx.compose.ui:ui&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-ui-ui = { module = &quot;androidx.compose.ui:ui&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.ui.ui"
                robot="true"
                replacement="libs.androidx.compose.ui.ui"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2692"
                    endOffset="2716"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="74"
            column="20"
            startOffset="2692"
            endLine="74"
            endColumn="44"
            endOffset="2716"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-ui-ui-graphics"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-ui-ui-graphics = { module = &quot;androidx.compose.ui:ui-graphics&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-ui-ui-graphics = { module = &quot;androidx.compose.ui:ui-graphics&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.ui.ui.graphics"
                robot="true"
                replacement="libs.androidx.compose.ui.ui.graphics"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2736"
                    endOffset="2769"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="75"
            column="20"
            startOffset="2736"
            endLine="75"
            endColumn="53"
            endOffset="2769"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-ui-ui-tooling-preview"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-ui-ui-tooling-preview = { module = &quot;androidx.compose.ui:ui-tooling-preview&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-ui-ui-tooling-preview = { module = &quot;androidx.compose.ui:ui-tooling-preview&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.ui.ui.tooling.preview"
                robot="true"
                replacement="libs.androidx.compose.ui.ui.tooling.preview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2789"
                    endOffset="2829"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="76"
            column="20"
            startOffset="2789"
            endLine="76"
            endColumn="60"
            endOffset="2829"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-material3-material3"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-material3-material3 = { module = &quot;androidx.compose.material3:material3&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-material3-material3 = { module = &quot;androidx.compose.material3:material3&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.material3.material3"
                robot="true"
                replacement="libs.androidx.compose.material3.material3"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2849"
                    endOffset="2887"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="77"
            column="20"
            startOffset="2849"
            endLine="77"
            endColumn="58"
            endOffset="2887"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for material-icons-core"
            robot="true">
            <fix-replace
                description="Replace with material-icons-core = { module = &quot;androidx.compose.material:material-icons-core&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="material-icons-core = { module = &quot;androidx.compose.material:material-icons-core&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.material.icons.core"
                robot="true"
                replacement="libs.material.icons.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2907"
                    endOffset="2954"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="78"
            column="20"
            startOffset="2907"
            endLine="78"
            endColumn="67"
            endOffset="2954"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for material-icons-extended"
            robot="true">
            <fix-replace
                description="Replace with material-icons-extended = { module = &quot;androidx.compose.material:material-icons-extended&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="material-icons-extended = { module = &quot;androidx.compose.material:material-icons-extended&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.material.icons.extended"
                robot="true"
                replacement="libs.material.icons.extended"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2989"
                    endOffset="3040"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="79"
            column="20"
            startOffset="2989"
            endLine="79"
            endColumn="71"
            endOffset="3040"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for gms-play-services-auth"
            robot="true">
            <fix-replace
                description="Replace with playServicesAuthVersion = &quot;21.0.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="playServicesAuthVersion = &quot;21.0.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with gms-play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuthVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gms-play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuthVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.gms.play.services.auth"
                robot="true"
                replacement="libs.gms.play.services.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3098"
                    endOffset="3148"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="82"
            column="20"
            startOffset="3098"
            endLine="82"
            endColumn="70"
            endOffset="3148"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3204"
                    endOffset="3243"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="85"
            column="20"
            startOffset="3204"
            endLine="85"
            endColumn="59"
            endOffset="3243"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.converter.gson"
                robot="true"
                replacement="libs.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3296"
                    endOffset="3341"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="86"
            column="20"
            startOffset="3296"
            endLine="86"
            endColumn="65"
            endOffset="3341"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-okhttp3-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with squareupLoggingInterceptorVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupLoggingInterceptorVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;squareupLoggingInterceptorVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;squareupLoggingInterceptorVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.okhttp3.logging.interceptor"
                robot="true"
                replacement="libs.com.squareup.okhttp3.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3361"
                    endOffset="3410"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="87"
            column="20"
            startOffset="3361"
            endLine="87"
            endColumn="69"
            endOffset="3410"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-gson"
            robot="true">
            <fix-replace
                description="Replace with gsonVersion = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gsonVersion = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.gson"
                robot="true"
                replacement="libs.google.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3474"
                    endOffset="3508"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="90"
            column="20"
            startOffset="3474"
            endLine="90"
            endColumn="54"
            endOffset="3508"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for auth-google-auth-library-oauth2-http"
            robot="true">
            <fix-replace
                description="Replace with googleAuthLibraryOauth2HttpVersion = &quot;1.23.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleAuthLibraryOauth2HttpVersion = &quot;1.23.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with auth-google-auth-library-oauth2-http = { module = &quot;com.google.auth:google-auth-library-oauth2-http&quot;, version.ref = &quot;googleAuthLibraryOauth2HttpVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="auth-google-auth-library-oauth2-http = { module = &quot;com.google.auth:google-auth-library-oauth2-http&quot;, version.ref = &quot;googleAuthLibraryOauth2HttpVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.auth.google.auth.library.oauth2.http"
                robot="true"
                replacement="libs.auth.google.auth.library.oauth2.http"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4071"
                    endOffset="4127"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="101"
            column="20"
            startOffset="4071"
            endLine="101"
            endColumn="76"
            endOffset="4127"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for auth-google-auth-library-credentials"
            robot="true">
            <fix-replace
                description="Replace with googleAuthLibraryCredentialsVersion = &quot;1.23.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleAuthLibraryCredentialsVersion = &quot;1.23.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with auth-google-auth-library-credentials = { module = &quot;com.google.auth:google-auth-library-credentials&quot;, version.ref = &quot;googleAuthLibraryCredentialsVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="auth-google-auth-library-credentials = { module = &quot;com.google.auth:google-auth-library-credentials&quot;, version.ref = &quot;googleAuthLibraryCredentialsVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.auth.google.auth.library.credentials"
                robot="true"
                replacement="libs.auth.google.auth.library.credentials"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4157"
                    endOffset="4213"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="102"
            column="20"
            startOffset="4157"
            endLine="102"
            endColumn="76"
            endOffset="4213"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for room-runtime"
            robot="true">
            <fix-replace
                description="Replace with roomRuntimeVersion = &quot;2.6.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="roomRuntimeVersion = &quot;2.6.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with room-runtime = { module = &quot;androidx.room:room-runtime&quot;, version.ref = &quot;roomRuntimeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="room-runtime = { module = &quot;androidx.room:room-runtime&quot;, version.ref = &quot;roomRuntimeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.room.runtime"
                robot="true"
                replacement="libs.room.runtime"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4256"
                    endOffset="4290"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="105"
            column="20"
            startOffset="4256"
            endLine="105"
            endColumn="54"
            endOffset="4290"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for room-compiler"
            robot="true">
            <fix-replace
                description="Replace with roomCompilerVersion = &quot;2.6.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="roomCompilerVersion = &quot;2.6.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with room-compiler = { module = &quot;androidx.room:room-compiler&quot;, version.ref = &quot;roomCompilerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="room-compiler = { module = &quot;androidx.room:room-compiler&quot;, version.ref = &quot;roomCompilerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.room.compiler"
                robot="true"
                replacement="libs.room.compiler"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4310"
                    endOffset="4345"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="106"
            column="10"
            startOffset="4310"
            endLine="106"
            endColumn="45"
            endOffset="4345"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for room-ktx"
            robot="true">
            <fix-replace
                description="Replace with roomKtxVersion = &quot;2.6.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="roomKtxVersion = &quot;2.6.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with room-ktx = { module = &quot;androidx.room:room-ktx&quot;, version.ref = &quot;roomKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="room-ktx = { module = &quot;androidx.room:room-ktx&quot;, version.ref = &quot;roomKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.room.ktx"
                robot="true"
                replacement="libs.room.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4365"
                    endOffset="4395"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="107"
            column="20"
            startOffset="4365"
            endLine="107"
            endColumn="50"
            endOffset="4395"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for work-runtime-ktx"
            robot="true">
            <fix-replace
                description="Replace with workRuntimeKtxVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="workRuntimeKtxVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with work-runtime-ktx = { module = &quot;androidx.work:work-runtime-ktx&quot;, version.ref = &quot;workRuntimeKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="work-runtime-ktx = { module = &quot;androidx.work:work-runtime-ktx&quot;, version.ref = &quot;workRuntimeKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.work.runtime.ktx"
                robot="true"
                replacement="libs.work.runtime.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4435"
                    endOffset="4473"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="110"
            column="20"
            startOffset="4435"
            endLine="110"
            endColumn="58"
            endOffset="4473"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for dagger-hilt-android"
            robot="true">
            <fix-replace
                description="Replace with hiltAndroidVersion = &quot;2.49&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="hiltAndroidVersion = &quot;2.49&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with dagger-hilt-android = { module = &quot;com.google.dagger:hilt-android&quot;, version.ref = &quot;hiltAndroidVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="dagger-hilt-android = { module = &quot;com.google.dagger:hilt-android&quot;, version.ref = &quot;hiltAndroidVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.dagger.hilt.android"
                robot="true"
                replacement="libs.dagger.hilt.android"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4565"
                    endOffset="4602"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="113"
            column="20"
            startOffset="4565"
            endLine="113"
            endColumn="57"
            endOffset="4602"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for dagger-hilt-compiler"
            robot="true">
            <fix-replace
                description="Replace with googleHiltCompiler = &quot;2.49&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleHiltCompiler = &quot;2.49&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with dagger-hilt-compiler = { module = &quot;com.google.dagger:hilt-compiler&quot;, version.ref = &quot;googleHiltCompiler&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="dagger-hilt-compiler = { module = &quot;com.google.dagger:hilt-compiler&quot;, version.ref = &quot;googleHiltCompiler&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.dagger.hilt.compiler"
                robot="true"
                replacement="libs.dagger.hilt.compiler"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4682"
                    endOffset="4720"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="114"
            column="10"
            startOffset="4682"
            endLine="114"
            endColumn="48"
            endOffset="4720"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for hilt-navigation-compose"
            robot="true">
            <fix-replace
                description="Replace with hiltNavigationComposeVersion = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="hiltNavigationComposeVersion = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with hilt-navigation-compose = { module = &quot;androidx.hilt:hilt-navigation-compose&quot;, version.ref = &quot;hiltNavigationComposeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="hilt-navigation-compose = { module = &quot;androidx.hilt:hilt-navigation-compose&quot;, version.ref = &quot;hiltNavigationComposeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.hilt.navigation.compose"
                robot="true"
                replacement="libs.hilt.navigation.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4814"
                    endOffset="4859"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="115"
            column="20"
            startOffset="4814"
            endLine="115"
            endColumn="65"
            endOffset="4859"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for hilt-work"
            robot="true">
            <fix-replace
                description="Replace with hiltWorkVersion = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="hiltWorkVersion = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with hilt-work = { module = &quot;androidx.hilt:hilt-work&quot;, version.ref = &quot;hiltWorkVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="hilt-work = { module = &quot;androidx.hilt:hilt-work&quot;, version.ref = &quot;hiltWorkVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.hilt.work"
                robot="true"
                replacement="libs.hilt.work"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4909"
                    endOffset="4940"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="116"
            column="20"
            startOffset="4909"
            endLine="116"
            endColumn="51"
            endOffset="4940"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-hilt-hilt-compiler"
            robot="true">
            <fix-replace
                description="Replace with androidxHiltCompiler = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxHiltCompiler = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-hilt-hilt-compiler = { module = &quot;androidx.hilt:hilt-compiler&quot;, version.ref = &quot;androidxHiltCompiler&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-hilt-hilt-compiler = { module = &quot;androidx.hilt:hilt-compiler&quot;, version.ref = &quot;androidxHiltCompiler&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="312"
                    endOffset="312"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.hilt.hilt.compiler"
                robot="true"
                replacement="libs.androidx.hilt.hilt.compiler"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="4980"
                    endOffset="5015"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="117"
            column="10"
            startOffset="4980"
            endLine="117"
            endColumn="45"
            endOffset="5015"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-viewmodel-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleViewmodelKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleViewmodelKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-viewmodel-ktx = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;lifecycleViewmodelKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-viewmodel-ktx = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;lifecycleViewmodelKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.viewmodel.ktx"
                robot="true"
                replacement="libs.lifecycle.viewmodel.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5120"
                    endOffset="5170"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="120"
            column="20"
            startOffset="5120"
            endLine="120"
            endColumn="70"
            endOffset="5170"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-viewmodel-compose"
            robot="true">
            <fix-replace
                description="Replace with lifecycleViewmodelComposeVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleViewmodelComposeVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="207"
                    endOffset="207"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-viewmodel-compose = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-compose&quot;, version.ref = &quot;lifecycleViewmodelComposeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-viewmodel-compose = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-compose&quot;, version.ref = &quot;lifecycleViewmodelComposeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.viewmodel.compose"
                robot="true"
                replacement="libs.lifecycle.viewmodel.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5200"
                    endOffset="5254"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="121"
            column="20"
            startOffset="5200"
            endLine="121"
            endColumn="74"
            endOffset="5254"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-runtime-compose"
            robot="true">
            <fix-replace
                description="Replace with lifecycleRuntimeComposeVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleRuntimeComposeVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="124"
                    endOffset="124"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-runtime-compose = { module = &quot;androidx.lifecycle:lifecycle-runtime-compose&quot;, version.ref = &quot;lifecycleRuntimeComposeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-runtime-compose = { module = &quot;androidx.lifecycle:lifecycle-runtime-compose&quot;, version.ref = &quot;lifecycleRuntimeComposeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1515"
                    endOffset="1515"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.runtime.compose"
                robot="true"
                replacement="libs.lifecycle.runtime.compose"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5284"
                    endOffset="5336"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="122"
            column="20"
            startOffset="5284"
            endLine="122"
            endColumn="72"
            endOffset="5336"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use the existing version catalog reference (`libs.junit`) instead">
        <fix-replace
            description="Replace with existing version catalog reference `junit`"
            robot="true"
            independent="true"
            replacement="libs.junit"
            priority="0">
            <range
                file="${:app*projectDir}/build.gradle"
                startOffset="5398"
                endOffset="5418"/>
        </fix-replace>
        <location
            file="${:app*projectDir}/build.gradle"
            line="125"
            column="24"
            startOffset="5398"
            endLine="125"
            endColumn="44"
            endOffset="5418"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (androidx.test.ext:junit is already available as `androidx-junit`, but using version 1.2.1 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for junit-v115"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with androidxJunitVersion = &quot;1.1.5&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="androidxJunitVersion = &quot;1.1.5&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="25"
                        endOffset="25"/>
                </fix-replace>
                <fix-replace
                    description="Replace with junit-v115 = { module = &quot;androidx.test.ext:junit&quot;, version.ref = &quot;androidxJunitVersion&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="junit-v115 = { module = &quot;androidx.test.ext:junit&quot;, version.ref = &quot;androidxJunitVersion&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="1515"
                        endOffset="1515"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.junit.v115"
                    robot="true"
                    independent="true"
                    replacement="libs.junit.v115"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="5449"
                        endOffset="5480"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `androidx-junit` (version 1.2.1)"
                replacement="libs.androidx.junit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5449"
                    endOffset="5480"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="126"
            column="31"
            startOffset="5449"
            endLine="126"
            endColumn="62"
            endOffset="5480"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead (androidx.test.espresso:espresso-core is already available as `androidx-espresso-core`, but using version 3.6.1 instead)">
        <fix-alternatives>
            <fix-composite
                description="Replace with new library catalog declaration for espresso-core-v351"
                robot="true"
                independent="true">
                <fix-replace
                    description="Replace with androidxEspressoCore = &quot;3.5.1&quot;..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="androidxEspressoCore = &quot;3.5.1&quot;&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="25"
                        endOffset="25"/>
                </fix-replace>
                <fix-replace
                    description="Replace with espresso-core-v351 = { module = &quot;androidx.test.espresso:espresso-core&quot;, version.ref = &quot;androidxEspressoCore&quot; }..."
                    robot="true"
                    independent="true"
                    oldString="_lint_insert_begin_"
                    replacement="espresso-core-v351 = { module = &quot;androidx.test.espresso:espresso-core&quot;, version.ref = &quot;androidxEspressoCore&quot; }&#xA;"
                    priority="0">
                    <range
                        file="../gradle/libs.versions.toml"
                        startOffset="312"
                        endOffset="312"/>
                </fix-replace>
                <fix-replace
                    description="Replace with libs.espresso.core.v351"
                    robot="true"
                    independent="true"
                    replacement="libs.espresso.core.v351"
                    priority="0">
                    <range
                        file="${:app*projectDir}/build.gradle"
                        startOffset="5511"
                        endOffset="5555"/>
                </fix-replace>
            </fix-composite>
            <fix-replace
                description="Replace with existing version catalog reference `androidx-espresso-core` (version 3.6.1)"
                replacement="libs.androidx.espresso.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5511"
                    endOffset="5555"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*projectDir}/build.gradle"
            line="127"
            column="31"
            startOffset="5511"
            endLine="127"
            endColumn="75"
            endOffset="5555"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-ui-ui-test-junit4"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-ui-ui-test-junit4 = { module = &quot;androidx.compose.ui:ui-test-junit4&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-ui-ui-test-junit4 = { module = &quot;androidx.compose.ui:ui-test-junit4&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.ui.ui.test.junit4"
                robot="true"
                replacement="libs.androidx.compose.ui.ui.test.junit4"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5586"
                    endOffset="5622"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="128"
            column="31"
            startOffset="5586"
            endLine="128"
            endColumn="67"
            endOffset="5622"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-ui-ui-tooling"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-ui-ui-tooling = { module = &quot;androidx.compose.ui:ui-tooling&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-ui-ui-tooling = { module = &quot;androidx.compose.ui:ui-tooling&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.ui.ui.tooling"
                robot="true"
                replacement="libs.androidx.compose.ui.ui.tooling"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5662"
                    endOffset="5694"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="129"
            column="25"
            startOffset="5662"
            endLine="129"
            endColumn="57"
            endOffset="5694"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-ui-ui-test-manifest"
            robot="true">
            <fix-replace
                description="Replace with androidx-compose-ui-ui-test-manifest = { module = &quot;androidx.compose.ui:ui-test-manifest&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-ui-ui-test-manifest = { module = &quot;androidx.compose.ui:ui-test-manifest&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="220"
                    endOffset="220"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.ui.ui.test.manifest"
                robot="true"
                replacement="libs.androidx.compose.ui.ui.test.manifest"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="5734"
                    endOffset="5772"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="130"
            column="25"
            startOffset="5734"
            endLine="130"
            endColumn="63"
            endOffset="5772"/>
    </incident>

</incidents>
