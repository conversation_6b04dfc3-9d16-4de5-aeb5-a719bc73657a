<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.abonekaptanmobile.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="293"
            endLine="8"
            endColumn="24"
            endOffset="305"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="29"
            endOffset="79"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="109"
            endLine="4"
            endColumn="29"
            endOffset="126"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="29"
            endOffset="173"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="203"
            endLine="6"
            endColumn="27"
            endOffset="218"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="248"
            endLine="7"
            endColumn="27"
            endOffset="263"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="335"
            endLine="9"
            endColumn="24"
            endOffset="347"/>
        <location id="R.string.error_loading_subscriptions"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1908"
            endLine="28"
            endColumn="47"
            endOffset="1942"/>
        <location id="R.string.feedback_submitted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="2001"
            endLine="29"
            endColumn="38"
            endOffset="2026"/>
        <entry
            name="model"
            string="color[purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),black(D),white(D)],drawable[ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),sign_in_prompt(U),sign_in_with_google(U),sign_in_button(U),refresh_subscriptions(U),sign_out(U),active_subscriptions(U),forgotten_subscriptions(U),cancelled_subscriptions(U),unknown_subscriptions(U),email_count(U),last_email_date(U),cancellation_date(U),is_this_wrong(U),feedback_dialog_title(U),current_status_is(U),why_is_this_wrong(U),feedback_option_is_subscription_active(U),feedback_option_is_subscription_forgotten(U),feedback_option_is_cancelled(U),feedback_option_not_a_subscription(U),feedback_option_other(U),feedback_note_other(U),submit_feedback(U),cancel(U),no_subscriptions_found(U),error_loading_subscriptions(D),feedback_submitted(D)],style[Theme_AboneKaptanMobile(U)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U)];8^9,a^7^8,b^7^8;;;"/>
    </map>

</incidents>
