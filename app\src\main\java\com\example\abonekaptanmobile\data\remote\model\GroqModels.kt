// file: app/java/com/example/abonekaptanmobile/data/remote/model/GroqModels.kt
package com.example.abonekaptanmobile.data.remote.model

import com.google.gson.annotations.SerializedName

/**
 * Turkish: Groq API'sine gönderilecek chat completion isteği.
 * English: Chat completion request to be sent to Groq API.
 */
data class GroqChatRequest(
    @SerializedName("model") val model: String = "deepseek-r1-distill-llama-70b",
    @SerializedName("messages") val messages: List<GroqMessage>,
    @SerializedName("temperature") val temperature: Float = 1.0f,
    @SerializedName("max_completion_tokens") val maxCompletionTokens: Int = 1024,
    @SerializedName("top_p") val topP: Float = 1.0f,
    @SerializedName("stream") val stream: Boolean = false,
    @SerializedName("stop") val stop: String? = null
)

/**
 * Turkish: Groq API mesaj modeli.
 * English: Groq API message model.
 */
data class GroqMessage(
    @SerializedName("role") val role: String, // "user", "assistant", "system"
    @SerializedName("content") val content: String
)

/**
 * Turkish: Groq API'den dönen yanıt modeli.
 * English: Response model returned from Groq API.
 */
data class GroqChatResponse(
    @SerializedName("id") val id: String? = null,
    @SerializedName("object") val objectType: String? = null,
    @SerializedName("created") val created: Long? = null,
    @SerializedName("model") val model: String? = null,
    @SerializedName("choices") val choices: List<GroqChoice>? = null,
    @SerializedName("usage") val usage: GroqUsage? = null,
    @SerializedName("error") val error: GroqError? = null
)

/**
 * Turkish: Groq API choice modeli.
 * English: Groq API choice model.
 */
data class GroqChoice(
    @SerializedName("index") val index: Int? = null,
    @SerializedName("message") val message: GroqMessage? = null,
    @SerializedName("finish_reason") val finishReason: String? = null
)

/**
 * Turkish: Groq API kullanım bilgisi.
 * English: Groq API usage information.
 */
data class GroqUsage(
    @SerializedName("prompt_tokens") val promptTokens: Int? = null,
    @SerializedName("completion_tokens") val completionTokens: Int? = null,
    @SerializedName("total_tokens") val totalTokens: Int? = null
)

/**
 * Turkish: Groq API hata modeli.
 * English: Groq API error model.
 */
data class GroqError(
    @SerializedName("message") val message: String? = null,
    @SerializedName("type") val type: String? = null,
    @SerializedName("code") val code: String? = null
)

/**
 * Turkish: Groq API'den dönen sınıflandırma sonucu.
 * English: Classification result returned from Groq API.
 */
data class GroqClassificationResult(
    val label: String,
    val confidence: Float,
    val reasoning: String? = null
) {
    companion object {
        /**
         * Turkish: Groq yanıtından sınıflandırma sonucu çıkarır.
         * English: Extracts classification result from Groq response.
         */
        fun fromGroqResponse(response: GroqChatResponse, candidateLabels: List<String>): GroqClassificationResult {
            val content = response.choices?.firstOrNull()?.message?.content ?: ""

            // JSON formatında yanıt bekliyoruz: {"label": "...", "confidence": 0.95, "reasoning": "..."}
            return try {
                val jsonRegex = Regex("""\{[^}]*\}""")
                val jsonMatch = jsonRegex.find(content)

                if (jsonMatch != null) {
                    val jsonStr = jsonMatch.value
                    parseJsonResponse(jsonStr, candidateLabels)
                } else {
                    // Fallback: Basit text parsing
                    parseTextResponse(content, candidateLabels)
                }
            } catch (e: Exception) {
                GroqClassificationResult("unknown", 0.0f, "Parse error: ${e.message}")
            }
        }

        private fun parseJsonResponse(jsonStr: String, candidateLabels: List<String>): GroqClassificationResult {
            // Basit JSON parsing (Gson kullanmadan)
            val labelMatch = Regex(""""label"\s*:\s*"([^"]+)"""").find(jsonStr)
            val confidenceMatch = Regex(""""confidence"\s*:\s*([0-9.]+)""").find(jsonStr)
            val reasoningMatch = Regex(""""reasoning"\s*:\s*"([^"]+)"""").find(jsonStr)

            val label = labelMatch?.groupValues?.get(1) ?: "unknown"
            val confidence = confidenceMatch?.groupValues?.get(1)?.toFloatOrNull() ?: 0.0f
            val reasoning = reasoningMatch?.groupValues?.get(1)

            return GroqClassificationResult(label, confidence, reasoning)
        }

        private fun parseTextResponse(content: String, candidateLabels: List<String>): GroqClassificationResult {
            // Candidate labels içinde geçen ilk label'ı bul
            val foundLabel = candidateLabels.find { label ->
                content.lowercase().contains(label.lowercase())
            } ?: "unknown"

            // Confidence için sayı ara
            val confidenceMatch = Regex("""([0-9.]+)""").find(content)
            val confidence = confidenceMatch?.value?.toFloatOrNull() ?: 0.5f

            return GroqClassificationResult(foundLabel, confidence, content.take(100))
        }
    }
}

/**
 * Turkish: İki aşamalı analiz için Groq sonucu.
 * English: Groq result for two-stage analysis.
 */
data class GroqTwoStageResult(
    val isSubscriptionCompany: Boolean,
    val companyName: String,
    val companyConfidence: Float,
    val reasoning: String? = null
) {
    companion object {
        fun fromGroqResponse(response: GroqChatResponse): GroqTwoStageResult {
            val content = response.choices?.firstOrNull()?.message?.content ?: ""

            return try {
                // JSON formatında yanıt bekliyoruz
                val jsonRegex = Regex("""\{[^}]*\}""")
                val jsonMatch = jsonRegex.find(content)

                if (jsonMatch != null) {
                    parseJsonStageResponse(jsonMatch.value)
                } else {
                    parseTextStageResponse(content)
                }
            } catch (e: Exception) {
                GroqTwoStageResult(false, "Unknown", 0.0f, "Parse error: ${e.message}")
            }
        }

        private fun parseJsonStageResponse(jsonStr: String): GroqTwoStageResult {
            val isSubscriptionMatch = Regex(""""is_subscription_company"\s*:\s*(true|false)""").find(jsonStr)
            val companyMatch = Regex(""""company_name"\s*:\s*"([^"]+)"""").find(jsonStr)
            val confidenceMatch = Regex(""""confidence"\s*:\s*([0-9.]+)""").find(jsonStr)
            val reasoningMatch = Regex(""""reasoning"\s*:\s*"([^"]+)"""").find(jsonStr)

            val isSubscription = isSubscriptionMatch?.groupValues?.get(1) == "true"
            val company = companyMatch?.groupValues?.get(1) ?: "Unknown"
            val confidence = confidenceMatch?.groupValues?.get(1)?.toFloatOrNull() ?: 0.0f
            val reasoning = reasoningMatch?.groupValues?.get(1)

            return GroqTwoStageResult(isSubscription, company, confidence, reasoning)
        }

        private fun parseTextStageResponse(content: String): GroqTwoStageResult {
            val isSubscription = content.lowercase().contains("true") ||
                                content.lowercase().contains("subscription") ||
                                content.lowercase().contains("abonelik")

            // Şirket adı için büyük harfle başlayan kelimeler ara
            val companyMatch = Regex("""([A-Z][a-zA-Z]+)""").find(content)
            val company = companyMatch?.value ?: "Unknown"

            val confidenceMatch = Regex("""([0-9.]+)""").find(content)
            val confidence = confidenceMatch?.value?.toFloatOrNull() ?: 0.5f

            return GroqTwoStageResult(isSubscription, company, confidence, content.take(100))
        }
    }
}

/**
 * Turkish: E-posta türü sınıflandırması için Groq sonucu.
 * English: Groq result for email type classification.
 */
data class GroqEmailTypeResult(
    val emailType: String,
    val confidence: Float,
    val reasoning: String? = null
) {
    companion object {
        fun fromGroqResponse(response: GroqChatResponse): GroqEmailTypeResult {
            val content = response.choices?.firstOrNull()?.message?.content ?: ""

            return try {
                val jsonRegex = Regex("""\{[^}]*\}""")
                val jsonMatch = jsonRegex.find(content)

                if (jsonMatch != null) {
                    parseJsonEmailTypeResponse(jsonMatch.value)
                } else {
                    parseTextEmailTypeResponse(content)
                }
            } catch (e: Exception) {
                GroqEmailTypeResult("unknown", 0.0f, "Parse error: ${e.message}")
            }
        }

        private fun parseJsonEmailTypeResponse(jsonStr: String): GroqEmailTypeResult {
            val typeMatch = Regex(""""email_type"\s*:\s*"([^"]+)"""").find(jsonStr)
            val confidenceMatch = Regex(""""confidence"\s*:\s*([0-9.]+)""").find(jsonStr)
            val reasoningMatch = Regex(""""reasoning"\s*:\s*"([^"]+)"""").find(jsonStr)

            val emailType = typeMatch?.groupValues?.get(1) ?: "unknown"
            val confidence = confidenceMatch?.groupValues?.get(1)?.toFloatOrNull() ?: 0.0f
            val reasoning = reasoningMatch?.groupValues?.get(1)

            return GroqEmailTypeResult(emailType, confidence, reasoning)
        }

        private fun parseTextEmailTypeResponse(content: String): GroqEmailTypeResult {
            val emailTypes = listOf("subscription_start", "subscription_cancel", "payment", "promotional", "none")
            val foundType = emailTypes.find { type ->
                content.lowercase().contains(type.lowercase())
            } ?: "unknown"

            val confidenceMatch = Regex("""([0-9.]+)""").find(content)
            val confidence = confidenceMatch?.value?.toFloatOrNull() ?: 0.5f

            return GroqEmailTypeResult(foundType, confidence, content.take(100))
        }
    }
}

/**
 * Turkish: Batch işleme için e-posta bilgisi.
 * English: Email information for batch processing.
 */
data class BatchEmailInfo(
    val emailIndex: Int,
    val domain: String,
    val subject: String
)

/**
 * Turkish: Batch işleme sonucu.
 * English: Batch processing result.
 */
data class BatchAnalysisResult(
    val results: List<TwoStageAnalysisResult>
) {
    companion object {
        fun fromGroqResponse(response: GroqChatResponse, emailInfos: List<BatchEmailInfo>): BatchAnalysisResult {
            val content = response.choices?.firstOrNull()?.message?.content ?: ""

            return try {
                // JSON array formatında yanıt bekliyoruz
                val results = parseBatchJsonResponse(content, emailInfos)
                BatchAnalysisResult(results)
            } catch (e: Exception) {
                // Hata durumunda boş sonuçlar döndür
                val emptyResults = emailInfos.map { emailInfo ->
                    TwoStageAnalysisResult(
                        emailIndex = emailInfo.emailIndex,
                        domain = emailInfo.domain,
                        subject = emailInfo.subject,
                        isSubscriptionCompany = false,
                        companyName = "Unknown",
                        companyConfidence = 0f
                    )
                }
                BatchAnalysisResult(emptyResults)
            }
        }

        private fun parseBatchJsonResponse(content: String, emailInfos: List<BatchEmailInfo>): List<TwoStageAnalysisResult> {
            val results = mutableListOf<TwoStageAnalysisResult>()

            // JSON array içindeki her bir objeyi parse et
            val jsonArrayRegex = Regex("""\[([^\]]*)\]""")
            val arrayMatch = jsonArrayRegex.find(content)

            if (arrayMatch != null) {
                val arrayContent = arrayMatch.groupValues[1]
                val objectRegex = Regex("""\{[^}]*\}""")
                val objectMatches = objectRegex.findAll(arrayContent)

                objectMatches.forEachIndexed { index, match ->
                    if (index < emailInfos.size) {
                        val emailInfo = emailInfos[index]
                        val result = parseSingleBatchResult(match.value, emailInfo)
                        results.add(result)
                    }
                }
            }

            // Eksik sonuçları tamamla
            while (results.size < emailInfos.size) {
                val emailInfo = emailInfos[results.size]
                results.add(
                    TwoStageAnalysisResult(
                        emailIndex = emailInfo.emailIndex,
                        domain = emailInfo.domain,
                        subject = emailInfo.subject,
                        isSubscriptionCompany = false,
                        companyName = "Unknown",
                        companyConfidence = 0f
                    )
                )
            }

            return results
        }

        private fun parseSingleBatchResult(jsonStr: String, emailInfo: BatchEmailInfo): TwoStageAnalysisResult {
            val isSubscriptionMatch = Regex(""""is_subscription_company"\s*:\s*(true|false)""").find(jsonStr)
            val companyMatch = Regex(""""company_name"\s*:\s*"([^"]+)"""").find(jsonStr)
            val confidenceMatch = Regex(""""confidence"\s*:\s*([0-9.]+)""").find(jsonStr)

            val isSubscription = isSubscriptionMatch?.groupValues?.get(1) == "true"
            val company = companyMatch?.groupValues?.get(1) ?: "Unknown"
            val confidence = confidenceMatch?.groupValues?.get(1)?.toFloatOrNull() ?: 0.0f

            return TwoStageAnalysisResult(
                emailIndex = emailInfo.emailIndex,
                domain = emailInfo.domain,
                subject = emailInfo.subject,
                isSubscriptionCompany = isSubscription,
                companyName = company,
                companyConfidence = confidence
            )
        }
    }
}