<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="conditional_incidents">

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`commons-logging` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <location
            file="${:app*projectDir}/build.gradle"/>
    </incident>

    <incident
        id="DuplicatePlatformClasses"
        severity="fatal"
        message="`httpclient` defines classes that conflict with classes now provided by Android. Solutions include finding newer versions or alternative libraries that don&apos;t have the same problem (for example, for `httpclient` use `HttpUrlConnection` or `okhttp` instead), or repackaging the library using something like `jarjar`.">
        <location
            file="${:app*projectDir}/build.gradle"
            line="94"
            column="25"
            startOffset="3661"
            endLine="94"
            endColumn="50"
            endOffset="3686"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1320"
                endOffset="1340"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="26"
            column="9"
            startOffset="1320"
            endLine="26"
            endColumn="29"
            endOffset="1340"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

</incidents>
