<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.0">

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return &quot;Company: $companyName (${String.format(&quot;%.2f&quot;, companyConfidence)}), &quot; +"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\data\remote\model\HuggingFaceModels.kt"
            line="130"
            column="42"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                &quot;Type: $emailType (${String.format(&quot;%.2f&quot;, emailTypeConfidence)}), &quot; +"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\data\remote\model\HuggingFaceModels.kt"
            line="131"
            column="38"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                &quot;Overall: ${String.format(&quot;%.2f&quot;, overallConfidence)}, &quot; +"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\data\remote\model\HuggingFaceModels.kt"
            line="132"
            column="29"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    &quot;Ortalama Güven&quot; to String.format(&quot;%.2f&quot;, stats.avgConfidence)"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt"
            line="157"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;Güven: ${String.format(&quot;%.2f&quot;, result.stage1_companyConfidence)}&quot;,"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt"
            line="234"
            column="42"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Güven: ${String.format(&quot;%.2f&quot;, result.stage1_companyConfidence)}&quot;,"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt"
            line="283"
            column="38"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;Güven: ${String.format(&quot;%.2f&quot;, confidence)}&quot;,"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt"
            line="336"
            column="42"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application or sdk runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="19"
            column="9"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.8.0 is available: 8.10.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.10.0 is difficult: 8.8.2)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.8.0&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="69"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.activity:activity-compose:1.8.2&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="70"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.android.gms:play-services-auth than 21.0.0 is available: 21.3.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.google.android.gms:play-services-auth:21.0.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="82"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.google.code.gson:gson:2.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="90"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.auth:google-auth-library-oauth2-http than 1.23.0 is available: 1.30.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.google.auth:google-auth-library-oauth2-http:1.23.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="101"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.auth:google-auth-library-credentials than 1.23.0 is available: 1.30.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.google.auth:google-auth-library-credentials:1.23.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="102"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.room:room-runtime:2.6.1&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="105"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    kapt &quot;androidx.room:room-compiler:2.6.1&quot;"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="106"
            column="10"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.room:room-ktx:2.6.1&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="107"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.work:work-runtime-ktx:2.9.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="110"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.hilt:hilt-navigation-compose:1.1.0&apos; // Stabil (veya 1.2.0-beta01)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="115"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.hilt:hilt-work:1.1.0&quot; // Stabil (veya 1.2.0-beta01)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="116"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.hilt:hilt-compiler than 1.1.0 is available: 1.2.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    kapt &quot;androidx.hilt:hilt-compiler:1.1.0&quot;      // (androidx olan, hilt-work ve hilt-navigation-compose için)"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="117"
            column="10"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="120"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="121"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-runtime-compose:2.7.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="122"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="126"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="127"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.15.0 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.15.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.8.7&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.8.7&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.8.7 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.8.7&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.04.01&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.04.01&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.04.01 is available: 2025.05.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.04.01&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\gradle\libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        severity="Warning"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        category="Security"
        priority="6"
        summary="Insecure TLS/SSL trust manager"
        explanation="This check looks for X509TrustManager implementations whose `checkServerTrusted` or `checkClientTrusted` methods do nothing (thus trusting any certificate chain) which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers."
        url="https://goo.gle/TrustAllX509TrustManager"
        urls="https://goo.gle/TrustAllX509TrustManager">
        <location
            file="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client\1.43.3\a758b82e55a2f5f681e289c5ed384d3dbda6f3cd\google-http-client-1.43.3.jar"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        severity="Warning"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        category="Security"
        priority="6"
        summary="Insecure TLS/SSL trust manager"
        explanation="This check looks for X509TrustManager implementations whose `checkServerTrusted` or `checkClientTrusted` methods do nothing (thus trusting any certificate chain) which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers."
        url="https://goo.gle/TrustAllX509TrustManager"
        urls="https://goo.gle/TrustAllX509TrustManager">
        <location
            file="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client\1.43.3\a758b82e55a2f5f681e289c5ed384d3dbda6f3cd\google-http-client-1.43.3.jar"/>
    </issue>

    <issue
        id="AcceptsUserCertificates"
        severity="Warning"
        message="The Network Security Configuration allows the use of user certificates in the release version of your app"
        category="Security"
        priority="5"
        summary="Allowing User Certificates"
        explanation="Allowing user certificates could allow eavesdroppers to intercept data sent by your app, which could impact the privacy of your users. Consider nesting your app&apos;s `trust-anchors` inside a `&lt;debug-overrides>` element to make sure they are only available when `android:debuggable` is set to `true`."
        url="https://goo.gle/AcceptsUserCertificates"
        urls="https://goo.gle/AcceptsUserCertificates,https://developer.android.com/training/articles/security-config#TrustingDebugCa"
        errorLine1="            &lt;certificates src=&quot;user&quot;/>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\xml\network_security_config.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="InsecureBaseConfiguration"
        severity="Warning"
        message="Insecure Base Configuration"
        category="Security"
        priority="5"
        summary="Insecure Base Configuration"
        explanation="Permitting cleartext traffic could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider only allowing encrypted traffic by setting the `cleartextTrafficPermitted` tag to `false`."
        url="https://goo.gle/InsecureBaseConfiguration"
        urls="https://goo.gle/InsecureBaseConfiguration,https://developer.android.com/preview/features/security-config.html"
        errorLine1="    &lt;base-config cleartextTrafficPermitted=&quot;true&quot;>"
        errorLine2="                                            ~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\xml\network_security_config.xml"
            line="14"
            column="45"/>
    </issue>

    <issue
        id="KaptUsageInsteadOfKsp"
        severity="Warning"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp"
        category="Performance"
        priority="4"
        summary="Kapt usage should be replaced with KSP"
        explanation="KSP is a more efficient replacement for kapt. For libraries that support both, KSP should be used to improve build times."
        url="https://developer.android.com/studio/build/migrate-to-ksp"
        urls="https://developer.android.com/studio/build/migrate-to-ksp"
        errorLine1="    kapt &quot;androidx.room:room-compiler:2.6.1&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="106"
            column="5"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        severity="Information"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        category="Performance"
        priority="3"
        summary="`State&lt;T>` will autobox values assigned to this state. Use a specialized state type instead."
        explanation="Calling `mutableStateOf&lt;T>()` when `T` is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for `Int`, `Long`, `Float`, and `Double` when the state does not need to track null values and does not override the default `SnapshotMutationPolicy`. See `mutableIntStateOf()`, `mutableLongStateOf()`, `mutableFloatStateOf()`, and `mutableDoubleStateOf()` for more information."
        errorLine1="    var selectedTab by remember { mutableStateOf(0) }"
        errorLine2="                                  ~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\java\com\example\abonekaptanmobile\ui\screens\StageAnalysisScreen.kt"
            line="41"
            column="35"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_500` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.black` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.white` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.error_loading_subscriptions` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;error_loading_subscriptions&quot;>Abonelikler yüklenirken hata oluştu.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\strings.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.feedback_submitted` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;feedback_submitted&quot;>Geri bildiriminiz gönderildi.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\res\values\strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (androidx.core:core-ktx is already available as `androidx-core-ktx`, but using version 1.15.0 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (androidx.lifecycle:lifecycle-runtime-ktx is already available as `androidx-lifecycle-runtime-ktx`, but using version 2.8.7 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="69"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (androidx.activity:activity-compose is already available as `androidx-activity-compose`, but using version 1.10.1 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.activity:activity-compose:1.8.2&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="70"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.squareup.okhttp3:logging-interceptor:4.12.0&quot;) // Veya OkHttp3&apos;ün diğer modülleriyle uyumlu versiyon"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="71"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.compose.ui:ui&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.compose.ui:ui-graphics&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="75"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.compose.ui:ui-tooling-preview&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="76"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.compose.material3:material3&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="77"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.compose.material:material-icons-core&apos; // BOM yönetir"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="78"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.compose.material:material-icons-extended&apos; // BOM yönetir"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="79"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.google.android.gms:play-services-auth:21.0.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="82"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.retrofit2:retrofit:2.9.0&apos; // Stabil, gerekirse güncelleyin"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="85"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.retrofit2:converter-gson:2.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="86"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.squareup.okhttp3:logging-interceptor:4.12.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="87"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.google.code.gson:gson:2.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="90"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.google.auth:google-auth-library-oauth2-http:1.23.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="101"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;com.google.auth:google-auth-library-credentials:1.23.0&apos; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="102"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.room:room-runtime:2.6.1&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="105"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    kapt &quot;androidx.room:room-compiler:2.6.1&quot;"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="106"
            column="10"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.room:room-ktx:2.6.1&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="107"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.work:work-runtime-ktx:2.9.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="110"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;com.google.dagger:hilt-android:2.49&quot; // Proje seviyesindeki plugin ile aynı versiyonu kullanın (örn: 2.49)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="113"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    kapt &quot;com.google.dagger:hilt-compiler:2.49&quot;     // Proje seviyesindeki plugin ile aynı versiyonu kullanın (örn: 2.49)"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="114"
            column="10"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &apos;androidx.hilt:hilt-navigation-compose:1.1.0&apos; // Stabil (veya 1.2.0-beta01)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="115"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.hilt:hilt-work:1.1.0&quot; // Stabil (veya 1.2.0-beta01)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="116"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    kapt &quot;androidx.hilt:hilt-compiler:1.1.0&quot;      // (androidx olan, hilt-work ve hilt-navigation-compose için)"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="117"
            column="10"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="120"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="121"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-runtime-compose:2.7.0&quot; // Stabil"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="122"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use the existing version catalog reference (`libs.junit`) instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    testImplementation &apos;junit:junit:4.13.2&apos;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="125"
            column="24"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (androidx.test.ext:junit is already available as `androidx-junit`, but using version 1.2.1 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="126"
            column="31"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead (androidx.test.espresso:espresso-core is already available as `androidx-espresso-core`, but using version 3.6.1 instead)"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="127"
            column="31"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    androidTestImplementation &apos;androidx.compose.ui:ui-test-junit4&apos; // BOM yönetir"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="128"
            column="31"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    debugImplementation &apos;androidx.compose.ui:ui-tooling&apos; // BOM yönetir"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="129"
            column="25"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    debugImplementation &apos;androidx.compose.ui:ui-test-manifest&apos; // BOM yönetir"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\build.gradle"
            line="130"
            column="25"/>
    </issue>

</issues>
