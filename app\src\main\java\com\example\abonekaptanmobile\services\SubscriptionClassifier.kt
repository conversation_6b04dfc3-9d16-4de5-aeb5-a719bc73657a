// file: app/java/com/example/abonekaptanmobile/services/SubscriptionClassifier.kt
package com.example.abonekaptanmobile.services

import android.util.Log
import com.example.abonekaptanmobile.data.local.entity.PatternType
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity
import com.example.abonekaptanmobile.data.remote.model.ClassificationResult
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository
import com.example.abonekaptanmobile.data.repository.GroqRepository
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository
import com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult
import com.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus
import com.example.abonekaptanmobile.data.remote.model.BatchEmailInfo
import com.example.abonekaptanmobile.model.*
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.util.concurrent.TimeUnit
import java.util.regex.PatternSyntaxException
import javax.inject.Inject

/**
 * Turkish: E-postaları abonelik durumuna göre sınıflandıran servis.
 * English: Service that classifies emails based on subscription status.
 */
class SubscriptionClassifier @Inject constructor(
    private val communityPatternRepo: CommunityPatternRepository,
    private val groqRepository: GroqRepository,
    private val stageAnalysisRepository: StageAnalysisRepository
) {
    private val inactivityThresholdDays = 90L
    private val inactivityThresholdMillis = TimeUnit.DAYS.toMillis(inactivityThresholdDays)

    // İptal kalıpları - Geliştirilmiş
    private val cancelPatterns = listOf(
        Regex("aboneliğiniz iptal edildi", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz sonlandırıldı", RegexOption.IGNORE_CASE),
        Regex("subscription cancelled", RegexOption.IGNORE_CASE),
        Regex("membership ended", RegexOption.IGNORE_CASE),
        Regex("your subscription has been canceled", RegexOption.IGNORE_CASE),
        Regex("hesabınız kapatıldı", RegexOption.IGNORE_CASE),
        Regex("account closed", RegexOption.IGNORE_CASE),
        Regex("iptal onaylandı", RegexOption.IGNORE_CASE),
        Regex("cancellation confirmed", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz sona erdi", RegexOption.IGNORE_CASE),
        Regex("aboneliğiniz sona ermiştir", RegexOption.IGNORE_CASE),
        Regex("subscription has been terminated", RegexOption.IGNORE_CASE),
        Regex("membership has been cancelled", RegexOption.IGNORE_CASE),
        Regex("we've processed your cancellation", RegexOption.IGNORE_CASE),
        Regex("your cancellation request", RegexOption.IGNORE_CASE),
        Regex("iptal talebiniz", RegexOption.IGNORE_CASE),
        Regex("aboneliğinizi iptal ettiniz", RegexOption.IGNORE_CASE)
    )

    // Abonelik başlangıç kalıpları
    private val startPatterns = listOf(
        Regex("aboneliğiniz başladı", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz aktifleştirildi", RegexOption.IGNORE_CASE),
        Regex("subscription activated", RegexOption.IGNORE_CASE),
        Regex("welcome to your .* subscription", RegexOption.IGNORE_CASE),
        Regex("your subscription has started", RegexOption.IGNORE_CASE),
        Regex("your membership has begun", RegexOption.IGNORE_CASE),
        Regex("aboneliğiniz başarıyla oluşturuldu", RegexOption.IGNORE_CASE),
        Regex("üyeliğiniz başarıyla başlatıldı", RegexOption.IGNORE_CASE),
        Regex("subscription confirmed", RegexOption.IGNORE_CASE),
        Regex("thank you for subscribing", RegexOption.IGNORE_CASE),
        Regex("abone olduğunuz için teşekkürler", RegexOption.IGNORE_CASE),
        Regex("hoş geldiniz", RegexOption.IGNORE_CASE),
        Regex("welcome to", RegexOption.IGNORE_CASE)
    )

    // Ödeme kalıpları
    private val paymentPatterns = listOf(
        Regex("aylık ödeme planı", RegexOption.IGNORE_CASE),
        Regex("yıllık ödeme planı", RegexOption.IGNORE_CASE),
        Regex("monthly subscription payment", RegexOption.IGNORE_CASE),
        Regex("annual subscription payment", RegexOption.IGNORE_CASE),
        Regex("faturanız", RegexOption.IGNORE_CASE),
        Regex("makbuzunuz", RegexOption.IGNORE_CASE),
        Regex("üyelik ücreti", RegexOption.IGNORE_CASE),
        Regex("payment receipt", RegexOption.IGNORE_CASE),
        Regex("invoice for your subscription", RegexOption.IGNORE_CASE),
        Regex("payment confirmation", RegexOption.IGNORE_CASE),
        Regex("ödeme onayı", RegexOption.IGNORE_CASE),
        Regex("ödemeniz alındı", RegexOption.IGNORE_CASE),
        Regex("payment received", RegexOption.IGNORE_CASE),
        Regex("\\d+[.,]\\d{2} (TL|USD|EUR|GBP)", RegexOption.IGNORE_CASE),
        Regex("\\$\\d+[.,]\\d{2}", RegexOption.IGNORE_CASE),
        Regex("€\\d+[.,]\\d{2}", RegexOption.IGNORE_CASE),
        Regex("£\\d+[.,]\\d{2}", RegexOption.IGNORE_CASE),
        Regex("\\d+[.,]\\d{2} ₺", RegexOption.IGNORE_CASE)
    )

    // Reklam kalıpları
    private val promotionalPatterns = listOf(
        Regex("özel teklif", RegexOption.IGNORE_CASE),
        Regex("special offer", RegexOption.IGNORE_CASE),
        Regex("limited time offer", RegexOption.IGNORE_CASE),
        Regex("sınırlı süre teklifi", RegexOption.IGNORE_CASE),
        Regex("discount", RegexOption.IGNORE_CASE),
        Regex("indirim", RegexOption.IGNORE_CASE),
        Regex("kampanya", RegexOption.IGNORE_CASE),
        Regex("promotion", RegexOption.IGNORE_CASE),
        Regex("deal", RegexOption.IGNORE_CASE),
        Regex("fırsat", RegexOption.IGNORE_CASE),
        Regex("kaçırma", RegexOption.IGNORE_CASE),
        Regex("don't miss", RegexOption.IGNORE_CASE),
        Regex("ücretsiz deneme", RegexOption.IGNORE_CASE),
        Regex("free trial", RegexOption.IGNORE_CASE)
    )

    // Güvenilir abonelik şirketlerinin domain listesi - SADECE GERÇEK ABONELİK SERVİSLERİ
    private val trustedSubscriptionDomains = setOf(
        // Video Streaming servisleri
        "netflix.com", "spotify.com", "disney.com", "disneyplus.com", "hulu.com",
        "hbomax.com", "max.com", "paramount.com", "paramountplus.com", "peacocktv.com",
        "crunchyroll.com", "twitch.tv", "youtube.com", "vimeo.com", "starz.com",
        "espn.com", "dazn.com", "mubi.com", "jiocinema.com", "globoplay.globo.com",
        "viu.com", "discoveryplus.com", "zee5.com", "rtlplus.de", "ivi.ru", "shahid.net",

        // Türk streaming servisleri
        "puhutv.com", "blutv.com", "exxen.com", "gain.tv", "tivibu.com.tr",
        "digiturk.com.tr", "dsmart.com.tr", "beinsports.com.tr", "tvplus.com.tr",

        // Müzik servisleri
        "spotify.com", "music.apple.com", "music.amazon.com", "tidal.com",
        "deezer.com", "soundcloud.com", "audible.com",

        // Kitap ve içerik
        "kindle.amazon.com", "storytel.com", "medium.com", "substack.com",

        // Eğitim platformları
        "coursera.org", "udemy.com", "linkedin.com/learning", "masterclass.com",
        "codecademy.com", "udacity.com", "skillshare.com", "duolingo.com",
        "babbel.com", "rosettastone.com", "brilliant.org", "pluralsight.com",

        // Sağlık ve fitness
        "peloton.com", "classpass.com", "fitness.apple.com", "fitbit.com",
        "calm.com", "headspace.com", "weightwatchers.com", "noom.com",
        "tonal.com", "mirror.co", "zwift.com", "betterhelp.com", "talkspace.com",
        "strava.com",

        // Finans ve haber
        "bloomberg.com", "wsj.com", "ft.com", "economist.com", "nytimes.com",
        "washingtonpost.com", "forbes.com", "barrons.com", "finance.yahoo.com",
        "seekingalpha.com", "morningstar.com", "reuters.com",

        // Oyun servisleri
        "xbox.com", "playstation.com", "nintendo.com", "ea.com", "arcade.apple.com",
        "ubisoft.com", "discord.com", "roblox.com",

        // Yazılım ve SaaS
        "microsoft.com", "office365.com", "workspace.google.com", "adobe.com",
        "salesforce.com", "aws.amazon.com", "azure.microsoft.com", "zoom.us",
        "slack.com", "atlassian.com", "dropbox.com", "box.com", "github.com",
        "gitlab.com", "canva.com", "asana.com", "trello.com", "docusign.com",
        "eventbrite.com", "grammarly.com", "webflow.com", "hubspot.com",
        "mailchimp.com", "surveymonkey.com", "zendesk.com", "servicenow.com",
        "twilio.com", "autodesk.com", "shopify.com",

        // Perakende üyelikler
        "amazon.com/prime", "costco.com", "samsclub.com",

        // Abonelik kutuları
        "hellofresh.com", "blueapron.com", "dollarshaveclub.com", "birchbox.com",
        "barkbox.com", "graze.com",

        // İçerik platformları
        "patreon.com", "onlyfans.com",

        // Productivity
        "evernote.com", "todoist.com", "notion.so", "monday.com",

        // Web hosting (sadece ücretli planlar)
        "squarespace.com", "wix.com", "wordpress.com", "godaddy.com",
        "bluehost.com", "hostgator.com", "siteground.com", "cloudflare.com",
        "digitalocean.com", "linode.com", "vultr.com", "heroku.com", "vercel.com",
        "netlify.com", "supabase.io", "planetscale.com",

        // Telekomünikasyon
        "turkcell.com.tr", "vodafone.com.tr", "turk-telekom.com.tr"

        // NOT: Google, Apple, Amazon (genel), LinkedIn, Facebook, Twitter, Instagram, TikTok
        // çıkarıldı çünkü bunlar çoğunlukla ücretsiz servisler ve bildirimler gönderiyorlar
    )





    /**
     * E-posta içeriğini sınıflandırma için hazırlar
     */
    private fun prepareEmailContentForClassification(email: RawEmail): String {
        val bodyContent = when {
            email.bodySnippet != null -> email.bodySnippet
            email.snippet != null -> email.snippet
            else -> email.bodyPlainText.take(500)
        }
        return "Subject: ${email.subject}\nFrom: ${email.from}\nContent: $bodyContent"
    }



    /**
     * E-posta adresinden domain adını çıkarır
     */
    private fun extractDomain(emailAddress: String): String? {
        val domainMatch = Regex("@([a-zA-Z0-9.-]+)").find(emailAddress)
        return domainMatch?.groupValues?.get(1)
    }

    /**
     * Şirket adlarını normalize eder - duplikasyonları önler
     */
    private fun normalizeCompanyName(companyName: String): String {
        val normalized = companyName.lowercase().trim()

        return when {
            // Spotify varyasyonları
            normalized.contains("spotify") -> "spotify"

            // Netflix varyasyonları
            normalized.contains("netflix") -> "netflix"

            // Disney varyasyonları
            normalized.contains("disney") -> "disney"

            // Adobe varyasyonları
            normalized.contains("adobe") -> "adobe"

            // Microsoft varyasyonları
            normalized.contains("microsoft") -> "microsoft"

            // Apple varyasyonları
            normalized.contains("apple") -> "apple"

            // Amazon varyasyonları
            normalized.contains("amazon") -> "amazon"

            // Google varyasyonları
            normalized.contains("google") -> "google"

            // Scribd varyasyonları
            normalized.contains("scribd") -> "scribd"

            // Financial Times varyasyonları (sadece gerçek Financial Times)
            normalized.contains("financial") && normalized.contains("times") -> "ft"

            // YouTube varyasyonları
            normalized.contains("youtube") -> "youtube"

            // LinkedIn varyasyonları
            normalized.contains("linkedin") -> "linkedin"

            // Diğer durumlarda orijinal adı döndür
            else -> companyName.lowercase()
        }
    }















    private fun extractGeneralServiceName(from: String, subject: String, bodySnippet: String?): String {
        val domainMatch = Regex("@([a-zA-Z0-9.-]+)").find(from)
        var serviceNameFromDomainPart = domainMatch?.groupValues?.get(1)?.substringBeforeLast(".")?.substringAfterLast(".")
        if (serviceNameFromDomainPart != null && serviceNameFromDomainPart.length < 3 && domainMatch?.groupValues?.get(1)?.contains(".") == true) {
            serviceNameFromDomainPart = domainMatch.groupValues[1].substringBeforeLast(".")
        }

        var serviceNameFromSenderDisplayName = from.substringBefore('<').trim().removeSurrounding("\"")
        if (serviceNameFromSenderDisplayName.contains("@") || serviceNameFromSenderDisplayName.length > 30 || serviceNameFromSenderDisplayName.isEmpty() || serviceNameFromSenderDisplayName.length < 3) {
            serviceNameFromSenderDisplayName = ""
        }

        val subjectKeywords = subject.split(Regex("[^a-zA-Z0-9İıÖöÜüÇçŞşĞğ]+"))
            .filter { it.length in 4..20 && it.firstOrNull()?.isLetter() == true && it.first().isUpperCase() && !it.matches(Regex("^[A-ZİÖÜüÇŞĞ]+$")) }
            .distinct()

        val commonDomainsToAvoidAsName = listOf("google", "googlemail", "gmail", "facebook", "microsoft", "apple", "amazon", "yahoo", "outlook", "hotmail", "support", "info", "noreply", "service", "team", "mail", "email", "com", "newsletter", "update", "alert", "bildirim", "duyuru", "haber", "mailchimp", "sendgrid")

        if (serviceNameFromSenderDisplayName.isNotBlank() && !commonDomainsToAvoidAsName.any { serviceNameFromSenderDisplayName.lowercase().contains(it) }) {
            return serviceNameFromSenderDisplayName.capitalizeWords()
        }

        if (subjectKeywords.isNotEmpty()) {
            val bestSubjectKeyword = subjectKeywords.firstOrNull { keyword -> !commonDomainsToAvoidAsName.any { keyword.lowercase().contains(it) } }
            if (bestSubjectKeyword != null) {
                return bestSubjectKeyword.capitalizeWords()
            }
        }

        val fullDomain = domainMatch?.groupValues?.get(1)
        if (fullDomain != null) {
            val parts = fullDomain.split('.')
            if (parts.isNotEmpty()) {
                val potentialNameFromDomain = if (parts.size > 1 && commonDomainsToAvoidAsName.contains(parts.getOrNull(parts.size - 2)?.lowercase())) {
                    if (parts.size > 2 && !commonDomainsToAvoidAsName.contains(parts.getOrNull(parts.size - 3)?.lowercase())) parts.getOrNull(parts.size - 3) else parts.firstOrNull()
                } else {
                    parts.firstOrNull()
                }
                if (potentialNameFromDomain != null && potentialNameFromDomain.length > 2 && !commonDomainsToAvoidAsName.contains(potentialNameFromDomain.lowercase()))
                    return potentialNameFromDomain.capitalizeWords()
            }
        }
        return serviceNameFromDomainPart?.capitalizeWords()?.takeIf { it.isNotBlank() && !commonDomainsToAvoidAsName.contains(it.lowercase()) } ?: "Unknown Service"
    }



    /**
     * Turkish: YENİ İKİ AŞAMALI SİSTEM - E-postaları iki aşamada analiz eder.
     * English: NEW TWO-STAGE SYSTEM - Analyzes emails in two stages.
     */
    suspend fun classifyEmailsTwoStage(
        allRawEmails: List<RawEmail>,
        onProgress: ((progress: Float, status: String) -> Unit)? = null
    ): List<SubscriptionItem> = coroutineScope {
        Log.i("SubscriptionClassifier", "🚀 YENİ İKİ AŞAMALI SİSTEM BAŞLADI - ${allRawEmails.size} e-posta analiz edilecek")

        val totalEmails = allRawEmails.size
        val startTime = System.currentTimeMillis()

        onProgress?.invoke(0f, "E-postalar hazırlanıyor...")

        // AŞAMA 1: Tüm e-postaların başlık ve domain'ini BATCH olarak analiz et
        Log.i("SubscriptionClassifier", "📧 AŞAMA 1: Batch başlık ve domain analizi başlıyor...")
        onProgress?.invoke(0.1f, "Aşama 1: E-posta başlıkları ve domain'leri batch olarak analiz ediliyor...")

        // E-postaları 20'li gruplar halinde hazırla
        val batchSize = 20
        val emailBatches = allRawEmails.mapIndexed { index, email ->
            BatchEmailInfo(
                emailIndex = index,
                domain = extractDomain(email.from) ?: "unknown",
                subject = email.subject
            )
        }.chunked(batchSize)

        val stage1Results = mutableListOf<TwoStageAnalysisResult>()
        val processedBatches = java.util.concurrent.atomic.AtomicInteger(0)

        val batchJobs = emailBatches.map { batch ->
            async {
                try {
                    Log.d("SubscriptionClassifier", "Batch işleniyor: ${batch.size} e-posta")

                    val batchResults = groqRepository.analyzeBatchCompanyFromDomainAndSubject(batch)

                    synchronized(stage1Results) {
                        stage1Results.addAll(batchResults)
                    }

                    // Batch sonuçlarını veritabanına kaydet
                    try {
                        val resultsWithEmails = batchResults.map { result ->
                            val email = allRawEmails[result.emailIndex]
                            Pair(result, email)
                        }
                        stageAnalysisRepository.saveStage1Results(resultsWithEmails)
                    } catch (e: Exception) {
                        Log.e("SubscriptionClassifier", "Batch sonuçları kaydetme hatası: ${e.message}", e)
                    }

                    // Progress güncelleme
                    val currentBatch = processedBatches.incrementAndGet()
                    val currentProgress = (currentBatch.toFloat() / emailBatches.size) * 0.4f + 0.1f // %10-50 arası
                    val elapsedTime = System.currentTimeMillis() - startTime
                    val avgTimePerBatch = if (currentBatch > 0) elapsedTime / currentBatch else 0
                    val remainingBatches = emailBatches.size - currentBatch
                    val estimatedRemainingTime = (remainingBatches * avgTimePerBatch) / 1000

                    val status = "Aşama 1: ${currentBatch}/${emailBatches.size} batch tamamlandı (${stage1Results.size}/${totalEmails} e-posta)" +
                                if (estimatedRemainingTime > 0) " (Tahmini ${estimatedRemainingTime.toInt()}s kaldı)" else ""

                    onProgress?.invoke(currentProgress, status)

                } catch (e: Exception) {
                    Log.e("SubscriptionClassifier", "Batch hatası: ${e.message}", e)
                    processedBatches.incrementAndGet()
                }
            }
        }

        // Tüm batch'lerin tamamlanmasını bekle
        batchJobs.awaitAll()

        // Abonelik şirketi olarak tespit edilen e-postaları filtrele
        val subscriptionEmails = stage1Results.filter { it.isSubscriptionCompany && it.companyConfidence >= 0.6f }
        Log.i("SubscriptionClassifier", "✅ AŞAMA 1 TAMAMLANDI: ${subscriptionEmails.size} abonelik e-postası tespit edildi")

        if (subscriptionEmails.isEmpty()) {
            onProgress?.invoke(1.0f, "Tamamlandı! Hiç abonelik e-postası bulunamadı.")
            return@coroutineScope emptyList()
        }

        // AŞAMA 2: Tespit edilen e-postaları eskiden yeniye sıralayıp türlerine göre sınıflandır
        Log.i("SubscriptionClassifier", "📊 AŞAMA 2: E-posta türü sınıflandırması başlıyor...")
        onProgress?.invoke(0.5f, "Aşama 2: E-posta türleri sınıflandırılıyor...")

        // E-postaları eskiden yeniye sırala
        val sortedSubscriptionEmails = subscriptionEmails.map { result ->
            val email = allRawEmails[result.emailIndex]
            result.copy(rawEmailContent = prepareEmailContentForClassification(email))
        }.sortedBy { allRawEmails[it.emailIndex].date } // En eski e-posta ilk

        val finalResults = mutableMapOf<String, FinalSubscriptionStatus>()
        val processedCount = java.util.concurrent.atomic.AtomicInteger(0)

        val stage2Jobs = sortedSubscriptionEmails.map { result ->
            async {
                try {
                    val email = allRawEmails[result.emailIndex]
                    val emailContent = result.rawEmailContent ?: prepareEmailContentForClassification(email)

                    val (emailType, confidence) = groqRepository.analyzeEmailTypeForCompany(
                        emailContent = emailContent,
                        companyName = result.companyName
                    )

                    // Sonuçları güncelle (aynı şirketten gelen yeni e-postalar önceki kayıtları günceller)
                    synchronized(finalResults) {
                        val normalizedCompanyName = normalizeCompanyName(result.companyName)

                        if (emailType != "none" && confidence >= 0.6f) {
                            finalResults[normalizedCompanyName] = FinalSubscriptionStatus(
                                companyName = normalizedCompanyName,
                                status = emailType,
                                lastEmailDate = email.date,
                                emailCount = (finalResults[normalizedCompanyName]?.emailCount ?: 0) + 1,
                                confidence = confidence
                            )

                            Log.d("SubscriptionClassifier", "✅ $normalizedCompanyName: $emailType (güven: ${String.format("%.2f", confidence)})")
                        }
                    }

                    // Progress güncelleme
                    val currentProcessed = processedCount.incrementAndGet()
                    val progress = 0.5f + (currentProcessed.toFloat() / sortedSubscriptionEmails.size) * 0.4f // %50-90 arası
                    val status = "Aşama 2: ${currentProcessed}/${sortedSubscriptionEmails.size} e-posta sınıflandırıldı"

                    onProgress?.invoke(progress, status)

                } catch (e: Exception) {
                    Log.e("SubscriptionClassifier", "Aşama 2 hatası: ${e.message}", e)
                    processedCount.incrementAndGet()
                }
            }
        }

        // Aşama 2'nin tamamlanmasını bekle
        stage2Jobs.awaitAll()

        // Final sonuçları SubscriptionItem'lara dönüştür
        onProgress?.invoke(0.95f, "Sonuçlar hazırlanıyor...")

        val subscriptionItems = finalResults.values.map { status ->
            createSubscriptionItemFromFinalStatus(status, allRawEmails)
        }

        Log.i("SubscriptionClassifier", "🎯 İKİ AŞAMALI SİSTEM TAMAMLANDI: ${subscriptionItems.size} abonelik oluşturuldu")
        subscriptionItems.forEach { subscription ->
            Log.i("SubscriptionClassifier", "✅ ${subscription.serviceName}: ${subscription.status.name} (${subscription.emailCount} e-posta)")
        }

        onProgress?.invoke(1.0f, "Tamamlandı! ${subscriptionItems.size} abonelik bulundu.")

        return@coroutineScope subscriptionItems
    }

    /**
     * Turkish: FinalSubscriptionStatus'tan SubscriptionItem oluşturur.
     * English: Creates SubscriptionItem from FinalSubscriptionStatus.
     */
    private fun createSubscriptionItemFromFinalStatus(
        status: FinalSubscriptionStatus,
        allEmails: List<RawEmail>
    ): SubscriptionItem {
        val subscriptionStatus = when (status.status) {
            "subscription_start" -> SubscriptionStatus.ACTIVE
            "subscription_cancel" -> SubscriptionStatus.CANCELLED
            else -> SubscriptionStatus.UNKNOWN
        }

        // Bu şirketten gelen e-postaları bul
        val companyEmails = allEmails.filter { email ->
            val domain = extractDomain(email.from) ?: ""
            normalizeCompanyName(extractGeneralServiceName(email.from, email.subject, email.bodySnippet)) == status.companyName
        }

        return SubscriptionItem(
            serviceName = status.companyName.capitalizeWords(),
            status = subscriptionStatus,
            emailCount = status.emailCount,
            lastEmailDate = status.lastEmailDate,
            cancellationDate = if (subscriptionStatus == SubscriptionStatus.CANCELLED) status.lastEmailDate else null,
            relatedEmailIds = companyEmails.map { it.id },
            subscriptionStartDate = companyEmails.minByOrNull { it.date }?.date
        )
    }
}

fun String.capitalizeWords(): String = this.split(Regex("\\s+")).joinToString(" ") { word ->
    if (word.isEmpty()) {
        ""
    } else {
        word.replaceFirstChar { char ->
            if (char.isLowerCase()) {
                char.titlecase()
            } else {
                char.toString()
            }
        }
    }
}