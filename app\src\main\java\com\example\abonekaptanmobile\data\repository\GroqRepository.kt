// file: app/java/com/example/abonekaptanmobile/data/repository/GroqRepository.kt
package com.example.abonekaptanmobile.data.repository

import android.util.Log
import com.example.abonekaptanmobile.data.remote.GroqApi
import com.example.abonekaptanmobile.data.remote.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Turkish: Groq API ile etkileşim için repository sınıfı.
 * English: Repository class for interacting with Groq API.
 */
@Singleton
class GroqRepository @Inject constructor(
    private val groqApi: GroqApi
) {
    companion object {
        private const val AUTH_TOKEN = "Bearer ********************************************************"
        private const val TAG = "GroqRepository"
        private const val MODEL_NAME = "meta-llama/llama-4-scout-17b-16e-instruct"
        private const val RATE_LIMIT_DELAY_MS = 1000L // 1 saniye bekleme (batch işleme için optimize edildi)

        // Sınıflandırma etiketleri
        private val SUBSCRIPTION_LABELS = listOf(
            "subscription_service", "streaming_platform", "software_service",
            "news_media", "fitness_app", "cloud_storage", "music_service",
            "video_service", "productivity_tool", "gaming_service", "other"
        )

        private val EMAIL_TYPE_LABELS = listOf(
            "subscription_start", "subscription_cancel", "subscription_renewal",
            "payment_confirmation", "promotional", "newsletter", "none"
        )

        // Güvenilir abonelik şirketleri listesi
        private val TRUSTED_SUBSCRIPTION_COMPANIES = setOf(
            // Streaming Platformları
            "netflix", "disney", "amazon prime", "hulu", "hbo", "paramount", "apple tv",
            "youtube premium", "spotify", "apple music", "deezer", "tidal",

            // Yazılım ve Bulut Servisleri
            "microsoft", "office 365", "adobe", "google", "dropbox", "icloud",
            "onedrive", "zoom", "slack", "notion", "evernote", "canva",

            // Oyun Platformları
            "playstation", "xbox", "nintendo", "steam", "epic games", "ubisoft",

            // Habercilik ve Medya
            "new york times", "wall street journal", "washington post", "medium",
            "kindle unlimited", "audible",

            // Fitness ve Sağlık
            "peloton", "fitbit", "myfitnesspal", "headspace", "calm",

            // Türk Servisleri
            "turkcell", "vodafone", "türk telekom", "digiturk", "beinsports",
            "netflix türkiye", "spotify türkiye", "youtube premium türkiye"
        )
    }

    /**
     * Turkish: Rate limit hatası durumunda retry yapar.
     * English: Retries in case of rate limit error.
     */
    private suspend fun <T> executeWithRetry(
        maxRetries: Int = 3,
        operation: suspend () -> T
    ): T {
        repeat(maxRetries) { attempt ->
            try {
                return operation()
            } catch (e: retrofit2.HttpException) {
                if (e.code() == 429 && attempt < maxRetries - 1) {
                    Log.w(TAG, "Rate limit hit, waiting ${RATE_LIMIT_DELAY_MS}ms before retry ${attempt + 1}")
                    delay(RATE_LIMIT_DELAY_MS)
                } else {
                    throw e
                }
            }
        }
        throw IllegalStateException("Max retries exceeded")
    }

    /**
     * Turkish: Verilen metni abonelik olup olmadığına göre sınıflandırır.
     * English: Classifies the given text as subscription or not.
     */
    suspend fun classifySubscription(emailContent: String): ClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying subscription for email content: ${emailContent.take(100)}...")

            val prompt = """
                Analyze the following email content and determine if it's from a subscription-based company.

                Email content: "$emailContent"

                Possible categories: ${SUBSCRIPTION_LABELS.joinToString(", ")}

                Respond with JSON format:
                {
                    "label": "category_name",
                    "confidence": 0.95,
                    "reasoning": "brief explanation"
                }
            """.trimIndent()

            val request = GroqChatRequest(
                model = MODEL_NAME,
                messages = listOf(
                    GroqMessage("system", "You are an expert email classifier. Always respond in JSON format."),
                    GroqMessage("user", prompt)
                ),
                temperature = 0.1f, // Düşük temperature tutarlı sonuçlar için
                maxCompletionTokens = 200
            )

            val response = groqApi.chatCompletion(AUTH_TOKEN, request = request)

            if (response.error != null) {
                Log.e(TAG, "Groq API error: ${response.error.message}")
                return@withContext ClassificationResult("error", 0f)
            }

            val result = GroqClassificationResult.fromGroqResponse(response, SUBSCRIPTION_LABELS)
            Log.d(TAG, "Classification result: ${result.label} (${result.confidence})")

            return@withContext ClassificationResult(result.label, result.confidence)
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying subscription: ${e.message}", e)
            return@withContext ClassificationResult("error", 0f)
        }
    }

    /**
     * Turkish: Verilen metni abonelik durumuna göre sınıflandırır (başlangıç, iptal, yenileme).
     * English: Classifies the given text based on subscription status (start, cancel, renewal).
     */
    suspend fun classifySubscriptionStatus(emailContent: String): ClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying subscription status for email content: ${emailContent.take(100)}...")

            val prompt = """
                Analyze the following email content and determine the subscription status.

                Email content: "$emailContent"

                Possible statuses: ${EMAIL_TYPE_LABELS.joinToString(", ")}

                Respond with JSON format:
                {
                    "label": "status_name",
                    "confidence": 0.95,
                    "reasoning": "brief explanation"
                }
            """.trimIndent()

            val request = GroqChatRequest(
                model = MODEL_NAME,
                messages = listOf(
                    GroqMessage("system", "You are an expert email classifier. Always respond in JSON format."),
                    GroqMessage("user", prompt)
                ),
                temperature = 0.1f,
                maxCompletionTokens = 200
            )

            val response = groqApi.chatCompletion(AUTH_TOKEN, request = request)

            if (response.error != null) {
                Log.e(TAG, "Groq API error: ${response.error.message}")
                return@withContext ClassificationResult("error", 0f)
            }

            val result = GroqClassificationResult.fromGroqResponse(response, EMAIL_TYPE_LABELS)
            Log.d(TAG, "Subscription status result: ${result.label} (${result.confidence})")

            return@withContext ClassificationResult(result.label, result.confidence)
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying subscription status: ${e.message}", e)
            return@withContext ClassificationResult("error", 0f)
        }
    }

    /**
     * Turkish: Verilen metni mail türüne göre detaylı olarak sınıflandırır.
     * English: Classifies the given text based on email type in detail.
     */
    suspend fun classifyEmailType(emailContent: String): DetailedClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying email type for content: ${emailContent.take(100)}...")

            val prompt = """
                Analyze the following email content and classify its type in detail.

                Email content: "$emailContent"

                Possible types: ${EMAIL_TYPE_LABELS.joinToString(", ")}

                Provide detailed analysis with confidence scores for top 3 categories.
                Respond with JSON format:
                {
                    "primary_label": "main_category",
                    "primary_confidence": 0.95,
                    "secondary_results": [
                        {"label": "category2", "confidence": 0.80},
                        {"label": "category3", "confidence": 0.60}
                    ],
                    "reasoning": "detailed explanation"
                }
            """.trimIndent()

            val request = GroqChatRequest(
                model = MODEL_NAME,
                messages = listOf(
                    GroqMessage("system", "You are an expert email classifier. Always respond in JSON format."),
                    GroqMessage("user", prompt)
                ),
                temperature = 0.1f,
                maxCompletionTokens = 300
            )

            val response = groqApi.chatCompletion(AUTH_TOKEN, request = request)

            if (response.error != null) {
                Log.e(TAG, "Groq API error: ${response.error.message}")
                return@withContext DetailedClassificationResult("error", 0f, emptyList())
            }

            val result = GroqClassificationResult.fromGroqResponse(response, EMAIL_TYPE_LABELS)
            Log.d(TAG, "Email type classification result: ${result.label} (${result.confidence})")

            // Basit bir DetailedClassificationResult oluştur
            val allResults = listOf(ClassificationResult(result.label, result.confidence))

            return@withContext DetailedClassificationResult(
                primaryLabel = result.label,
                primaryScore = result.confidence,
                allResults = allResults
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying email type: ${e.message}", e)
            return@withContext DetailedClassificationResult("error", 0f, emptyList())
        }
    }

    /**
     * Turkish: İki aşamalı analiz - Aşama 1: Domain ve subject'ten şirket tespiti.
     * English: Two-stage analysis - Stage 1: Company detection from domain and subject.
     */
    suspend fun analyzeCompanyFromDomainAndSubject(
        emailIndex: Int,
        domain: String,
        subject: String
    ): TwoStageAnalysisResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Aşama 1: Analyzing company from domain: $domain, subject: $subject")

            val prompt = """
                Analyze the email domain and subject to determine if this is from a subscription-based company.

                Domain: $domain
                Subject: $subject

                Determine:
                1. Is this from a subscription-based company? (true/false)
                2. What is the company name?
                3. Confidence level (0.0 to 1.0)

                Respond with JSON format:
                {
                    "is_subscription_company": true,
                    "company_name": "Netflix",
                    "confidence": 0.95,
                    "reasoning": "Netflix is a well-known streaming subscription service"
                }
            """.trimIndent()

            val request = GroqChatRequest(
                model = MODEL_NAME,
                messages = listOf(
                    GroqMessage("system", "You are an expert at identifying subscription-based companies. Always respond in JSON format."),
                    GroqMessage("user", prompt)
                ),
                temperature = 0.1f,
                maxCompletionTokens = 200
            )

            val response = groqApi.chatCompletion(AUTH_TOKEN, request = request)

            if (response.error != null) {
                Log.e(TAG, "Groq API error: ${response.error.message}")
                return@withContext TwoStageAnalysisResult(
                    emailIndex = emailIndex,
                    domain = domain,
                    subject = subject,
                    isSubscriptionCompany = false,
                    companyName = "Unknown",
                    companyConfidence = 0f
                )
            }

            val result = GroqTwoStageResult.fromGroqResponse(response)
            Log.d(TAG, "Aşama 1 Sonuç: Şirket=${result.companyName}, Güven=${result.companyConfidence}, Abonelik=${result.isSubscriptionCompany}")

            // Güvenilir şirket kontrolü
            val isTrustedCompany = TRUSTED_SUBSCRIPTION_COMPANIES.any { trustedCompany ->
                result.companyName.lowercase().contains(trustedCompany.lowercase()) ||
                domain.lowercase().contains(trustedCompany.lowercase())
            }

            val finalIsSubscription = result.isSubscriptionCompany && isTrustedCompany

            Log.d(TAG, "Güvenilir şirket kontrolü: ${result.companyName} -> Güvenilir: $isTrustedCompany, Final: $finalIsSubscription")

            return@withContext TwoStageAnalysisResult(
                emailIndex = emailIndex,
                domain = domain,
                subject = subject,
                isSubscriptionCompany = finalIsSubscription,
                companyName = result.companyName,
                companyConfidence = if (finalIsSubscription) result.companyConfidence else 0f
            )
        } catch (e: Exception) {
            Log.e(TAG, "Aşama 1 hatası: ${e.message}", e)
            return@withContext TwoStageAnalysisResult(
                emailIndex = emailIndex,
                domain = domain,
                subject = subject,
                isSubscriptionCompany = false,
                companyName = "Unknown",
                companyConfidence = 0f
            )
        }
    }

    /**
     * Turkish: İki aşamalı analiz - Aşama 2: E-posta türü belirleme.
     * English: Two-stage analysis - Stage 2: Email type determination.
     */
    suspend fun analyzeEmailTypeForCompany(
        emailContent: String,
        companyName: String
    ): Pair<String, Float> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Aşama 2: Analyzing email type for company: $companyName")

            val prompt = """
                Analyze this email content from $companyName to determine the email type.

                Company: $companyName
                Email content: "$emailContent"

                Possible email types:
                - subscription_start: New subscription confirmation
                - subscription_cancel: Subscription cancellation
                - subscription_renewal: Subscription renewal/billing
                - payment_confirmation: Payment receipt
                - promotional: Marketing/promotional content
                - newsletter: Regular newsletter
                - none: Not subscription-related

                Respond with JSON format:
                {
                    "email_type": "subscription_start",
                    "confidence": 0.95,
                    "reasoning": "This email confirms a new subscription signup"
                }
            """.trimIndent()

            val request = GroqChatRequest(
                model = MODEL_NAME,
                messages = listOf(
                    GroqMessage("system", "You are an expert at classifying subscription-related emails. Always respond in JSON format."),
                    GroqMessage("user", prompt)
                ),
                temperature = 0.1f,
                maxCompletionTokens = 200
            )

            val response = groqApi.chatCompletion(AUTH_TOKEN, request = request)

            if (response.error != null) {
                Log.e(TAG, "Groq API error: ${response.error.message}")
                return@withContext Pair("none", 0f)
            }

            val result = GroqEmailTypeResult.fromGroqResponse(response)
            Log.d(TAG, "Aşama 2 Sonuç: Tür=${result.emailType}, Güven=${result.confidence}")

            return@withContext Pair(result.emailType, result.confidence)
        } catch (e: Exception) {
            Log.e(TAG, "Aşama 2 hatası: ${e.message}", e)
            return@withContext Pair("none", 0f)
        }
    }

    /**
     * Turkish: Batch işleme - 20'li gruplar halinde e-posta analizi.
     * English: Batch processing - Email analysis in groups of 20.
     */
    suspend fun analyzeBatchCompanyFromDomainAndSubject(
        emailInfos: List<BatchEmailInfo>
    ): List<TwoStageAnalysisResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Batch Aşama 1: Analyzing ${emailInfos.size} emails in batch")

            // 20'li gruplar halinde prompt oluştur
            val emailsText = emailInfos.mapIndexed { index, emailInfo ->
                """
                Email ${index + 1}:
                Domain: ${emailInfo.domain}
                Subject: ${emailInfo.subject}
                """.trimIndent()
            }.joinToString("\n\n")

            val prompt = """
                Analyze the following ${emailInfos.size} emails to determine if each is from a subscription-based company.

                $emailsText

                For each email, determine:
                1. Is this from a subscription-based company? (true/false)
                2. What is the company name?
                3. Confidence level (0.0 to 1.0)

                Respond with JSON array format:
                [
                    {
                        "is_subscription_company": true,
                        "company_name": "Netflix",
                        "confidence": 0.95
                    },
                    {
                        "is_subscription_company": false,
                        "company_name": "Unknown",
                        "confidence": 0.1
                    }
                ]

                Provide exactly ${emailInfos.size} results in the same order as the emails.
            """.trimIndent()

            val request = GroqChatRequest(
                model = MODEL_NAME,
                messages = listOf(
                    GroqMessage("system", "You are an expert at identifying subscription-based companies. Always respond in JSON array format with exactly the same number of results as input emails."),
                    GroqMessage("user", prompt)
                ),
                temperature = 0.1f,
                maxCompletionTokens = 1200 // Daha fazla token 20'li batch işleme için
            )

            val response = executeWithRetry {
                groqApi.chatCompletion(AUTH_TOKEN, request = request)
            }

            if (response.error != null) {
                Log.e(TAG, "Groq API error in batch: ${response.error.message}")
                // Hata durumunda boş sonuçlar döndür
                return@withContext emailInfos.map { emailInfo ->
                    TwoStageAnalysisResult(
                        emailIndex = emailInfo.emailIndex,
                        domain = emailInfo.domain,
                        subject = emailInfo.subject,
                        isSubscriptionCompany = false,
                        companyName = "Unknown",
                        companyConfidence = 0f
                    )
                }
            }

            val batchResult = BatchAnalysisResult.fromGroqResponse(response, emailInfos)
            Log.d(TAG, "Batch Aşama 1 Sonuç: ${batchResult.results.size} e-posta analiz edildi")

            // Güvenilir şirket kontrolü uygula
            val finalResults = batchResult.results.map { result ->
                val isTrustedCompany = TRUSTED_SUBSCRIPTION_COMPANIES.any { trustedCompany ->
                    result.companyName.lowercase().contains(trustedCompany.lowercase()) ||
                    result.domain.lowercase().contains(trustedCompany.lowercase())
                }

                val finalIsSubscription = result.isSubscriptionCompany && isTrustedCompany

                result.copy(
                    isSubscriptionCompany = finalIsSubscription,
                    companyConfidence = if (finalIsSubscription) result.companyConfidence else 0f
                )
            }

            Log.d(TAG, "Batch güvenilir şirket kontrolü tamamlandı: ${finalResults.count { it.isSubscriptionCompany }} güvenilir şirket")

            return@withContext finalResults
        } catch (e: Exception) {
            Log.e(TAG, "Batch Aşama 1 hatası: ${e.message}", e)
            // Hata durumunda boş sonuçlar döndür
            return@withContext emailInfos.map { emailInfo ->
                TwoStageAnalysisResult(
                    emailIndex = emailInfo.emailIndex,
                    domain = emailInfo.domain,
                    subject = emailInfo.subject,
                    isSubscriptionCompany = false,
                    companyName = "Unknown",
                    companyConfidence = 0f
                )
            }
        }
    }
}
